# Malawi Test Catalog

## Overview
Malawi Test Catalog is a web application built using [Nuxt 3](https://nuxt.com/) to provide a structured and efficient way to manage and browse malawi laboratory tests. The app follows modern development best practices and utilizes TypeScript, Tailwind CSS, and a modular architecture.

## Features
- **Nuxt 3 framework** for server-side rendering (SSR) and static site generation (SSG).
- **TypeScript support** for better code maintainability.
- **Tailwind CSS** for responsive and modern UI design.
- **Modular architecture** with separate folders for components, pages, middleware, and utilities.
- **Environment variable support** via `.env` files.
- **API handling** with dedicated types and endpoints. It uses [NLIMS](https://github.com/HISMalawi/nlims_controller) API for data retrieval.
- **State ** using Nuxt Store.

## Project Structure
```

├── assets/              # Static assets (images, fonts, etc.)
├── components/          # Vue components
├── layouts/             # Layout components
├── middleware/          # Page Routing middleware
├── modules/             # Custom Nuxt modules
├── node_modules/        # Dependencies
├── pages/               # Application pages (routes)
├── plugins/             # Nuxt plugins
├── public/              # Static public files
├── server/              # Backend API logic
├── store/               # Pinia Store State 
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
├── .env                 # Environment variables
├── .gitignore           # Git ignored files
├── nuxt.config.ts       # Nuxt configuration
├── package.json         # Project metadata and dependencies
├── tailwind.config.ts   # Tailwind CSS configuration
├── tsconfig.json        # TypeScript configuration
└── README.md            # Project documentation
```

## Installation
To set up the project locally, follow these steps:

### Prerequisites
Ensure you have the following installed:
- [Node.js](https://nodejs.org/) (Latest LTS version recommended)
- [Yarn](https://yarnpkg.com/) or npm

### Clone the Repository
```sh
git clone https://github.com/EGPAFMalawiHIS/Malawi-Test-Catalog.git
cd malawi-test-catalog
```

### Install Dependencies
```sh
npm install
```

## Environment Variables
Create a `.env` file based on `.env.example` and configure API_BASE_URL to point to [NLIMS](https://github.com/HISMalawi/nlims_controller).

### Run Development Server
```sh
npm run dev 
```
Open [http://localhost:3000](http://localhost:3000) to see the app in action.

## Deployment
To build and deploy the Nuxt 3 app:
```sh
npm run build
npm run start
```
## Contributing
Contributions are welcome! Feel free to open an issue or submit a pull request.
