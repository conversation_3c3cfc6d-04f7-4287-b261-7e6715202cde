import { ref, reactive } from 'vue';
import type {
    TestCatalog,
    Measure,
    Department,
    MeasureType,
    MeasureAutocomplete,
    MeasureRange,
} from "@/types";
import type { FormErrors } from '@/types/input';

export function useTestType() {
    const currentStep = ref<number>(1);
    const loading = ref<boolean>(false);
    const departments = ref<Department[]>([]);
    const measureTypes = ref<MeasureType[]>([]);
    const validationErrors = reactive({});

    const stepStatuses = ref({
        1: "neutral",
        2: "neutral",
        3: "neutral",
        4: "neutral",
        5: "neutral",
    });

    const testCatalog = reactive<TestCatalog>(createEmptyTestCatalog());

    function createEmptyTestCatalog(): TestCatalog {
        return {
            test_type: {
                name: "",
                short_name: "",
                description: "",
                loinc_code: "",
                moh_code: "",
                nlims_code: "",
                targetTAT: "",
                preferred_name: "",
                scientific_name: "",
                can_be_done_on_sex: "",
                test_category_id: 0,
                id: 0,
                prevalence_threshold: "",
                created_at: "",
                updated_at: "",
                assay_format: null,
                hr_cadre_required: null,
                iblis_mapping_name: null,
            },
            specimen_types: [],
            measures: [createEmptyMeasure()],
            organisms: [],
            lab_test_sites: [],
            equipment: [],
        };
    }

    function createEmptyMeasure(): Measure {
        return {
            name: "",
            short_name: "",
            unit: "",
            measure_type_id: 6,
            description: "",
            loinc_code: "",
            moh_code: "",
            nlims_code: "",
            preferred_name: "",
            scientific_name: "",
            measure_ranges_attributes: [],
        };
    }

    function removeNullKeys<T>(obj: T): T extends object ? Partial<T> : T {
        if (typeof obj !== "object" || obj === null) {
            return obj as any;
        }

        if (Array.isArray(obj)) {
            return obj.map((item) => removeNullKeys(item)) as any;
        }

        const result: Record<string, any> = {};

        for (const key in obj) {
            if (
                Object.prototype.hasOwnProperty.call(obj, key) &&
                obj[key as keyof typeof obj] !== null
            ) {
                result[key] = removeNullKeys(obj[key as keyof typeof obj]);
            }
        }

        return result as any;
    }

    const handleStepChange = (stepId: number): void => {
        currentStep.value = stepId;
    };

    const getValidationErrors = (errors: FormErrors): void => {
        Object.assign(validationErrors, errors);
    };

    const handleSpecimenUpdate = (selectedSpecimens: number[]): void => {
        testCatalog.specimen_types = selectedSpecimens;
    };

    const handleOrganismsUpdate = (selectedOrganisms: number[]): void => {
        testCatalog.organisms = selectedOrganisms;
    };

    const prepareDataForSubmission = () => {
        const dataCopy = JSON.parse(JSON.stringify(testCatalog));

        if (dataCopy.test_type && "id" in dataCopy.test_type) {
            delete dataCopy.test_type.id;
        }

        if (dataCopy.measures && dataCopy.measures.length > 0) {
            dataCopy.measures.forEach(
                (measure: { measure_ranges_attributes: any[] }) => {
                    if (
                        measure.measure_ranges_attributes &&
                        measure.measure_ranges_attributes.length > 0
                    ) {
                        measure.measure_ranges_attributes.forEach((range) => {
                            if ("id" in range) {
                                delete range.id;
                            }
                        });
                    }
                }
            );
        }

        return dataCopy;
    };

    const getSteps = () => {
        return [
            {
                id: 1,
                title: "Basic Information",
                component: defineAsyncComponent(() => import("@/components/test-types/BasicInformation.vue")),
                props: {
                    testCatalog: testCatalog,
                    errors: validationErrors,
                    departments: departments.value.map((department: Department) => {
                        return {
                            id: department.id,
                            label: department.name,
                        };
                    }),
                },
                validationSchema: {
                    fields: [
                        {
                            name: "test_type_name",
                            label: "Test Type Name",
                            rules: { required: true },
                        },
                        {
                            name: "short_name",
                            label: "Short Name",
                            rules: { required: false },
                        },
                        {
                            name: "preferred_name",
                            label: "Preferred Name",
                            rules: { required: true },
                        },
                        {
                            name: "scientific_name",
                            label: "Scientific Name",
                            rules: { required: true },
                        },
                        {
                            name: "target_tat",
                            label: "Target TAT",
                            rules: { required: false },
                        },
                        {
                            name: "iblis_mapping_name",
                            label: "IBLIS Mapping Name",
                            rules: { required: false },
                        },
                        {
                            name: "can_be_done_on_sex",
                            label: "Can be done on sex",
                            rules: { required: true },
                        },
                        {
                            name: "test_category_id",
                            label: "Test Category",
                            rules: {
                                custom(value: any, formValues: { test_category_id: number; }) {
                                    if (formValues.test_category_id === 0) {
                                        return "Test category is required";
                                    }
                                    return true;
                                },
                            },
                        },
                    ],
                },
                values: { test_type_name: "" },
            },
            {
                id: 2,
                title: "Specimens",
                component: defineAsyncComponent(() => import("@/components/test-types/SpecimenType.vue")),
                props: {
                    data: testCatalog,
                    errors: validationErrors
                },
                validationSchema: {
                    fields: [
                        {
                            name: "specimen_types",
                            label: "Specimen Types",
                            rules: { required: true },
                        },
                    ],
                },
            },
            {
                id: 3,
                title: "Measures",
                component: defineAsyncComponent(() => import("@/components/test-types/Measure.vue")),
                props: {
                    initialTestCatalog: testCatalog,
                    testCatalog: testCatalog,
                    errors: validationErrors
                },
                validationSchema: {
                    fields: [
                        {
                            name: "measures",
                            label: "Measures",
                            rules: {
                                custom: (measures: Measure[]) => {
                                    if (measures == null) return "";
                                    if (measures.length === 0) {
                                        return "At least one measure must be added";
                                    }

                                    if (!measures.every((measure) => measure.name.trim() !== "")) {
                                        return "All measures must have a name";
                                    }

                                    if (
                                        !measures.every(
                                            (measure) => measure.preferred_name.trim() !== ""
                                        )
                                    ) {
                                        return "Preferred name is required for all measures";
                                    }

                                    if (
                                        !measures.every(
                                            (measure) => measure.scientific_name.trim() !== ""
                                        )
                                    ) {
                                        return "Scientific name is required for all measures";
                                    }

                                    if (
                                        !measures.every((measure) => measure.short_name.trim() !== "")
                                    ) {
                                        return "All measures must have a short name";
                                    }

                                    if (!measures.every((measure) => measure.measure_type_id > 0)) {
                                        return "All measures must have a measure type";
                                    }

                                    const rangeMeasureTypes = measureTypes.value.filter(
                                        (measureType) =>
                                            measureType.structure?.type === "ranges" ||
                                            measureType.structure?.type === "options"
                                    );
                                    const rangeMeasureTypeIds = new Set(
                                        rangeMeasureTypes.map((measureType) => measureType.id)
                                    );

                                    const measuresWithRangeTypes = measures.filter(
                                        (measure: Measure) =>
                                            rangeMeasureTypeIds.has(measure.measure_type_id)
                                    );

                                    if (measuresWithRangeTypes.length > 0) {
                                        if (
                                            measuresWithRangeTypes.some(
                                                (measure: Measure) =>
                                                    measure.measure_ranges_attributes.length === 0
                                            )
                                        ) {
                                            return "Measures with range or option types require at least one range";
                                        }

                                        if (
                                            measuresWithRangeTypes.some((measure: Measure) =>
                                                measure.measure_ranges_attributes.some(
                                                    (range: MeasureRange | MeasureAutocomplete) => {
                                                        if ("range_lower" in range && "range_upper" in range) {
                                                            return (
                                                                range.range_lower === null ||
                                                                range.range_upper === null
                                                            );
                                                        }
                                                        if ("value" in range && "interpretation" in range) {
                                                            return (
                                                                range.value === null ||
                                                                range.interpretation === null
                                                            );
                                                        }
                                                        return false;
                                                    }
                                                )
                                            )
                                        ) {
                                            return "All measures require complete range values or option values with interpretations";
                                        }
                                    }

                                    return true;
                                },
                            },
                        },
                    ],
                },
            },
            {
                id: 4,
                title: "Organisms",
                component: defineAsyncComponent(() => import("@/components/test-types/Organisms.vue")),
                props: {
                    testCatalog: testCatalog,
                    selected: testCatalog.organisms,
                },
                validationSchema: { fields: [] },
            },
            {
                id: 5,
                title: "Others",
                component: defineAsyncComponent(() => import("@/components/test-types/Other.vue")),
                props: {
                    testCatalog: testCatalog,
                    currentStep: currentStep.value,
                },
                validationSchema: {
                    fields: [
                        {
                            name: "assay_format",
                            label: "Assay Format",
                            rules: {
                                required: false,
                            },
                        },
                        {
                            name: "equipment",
                            label: "Equipment Required",
                            rules: {
                                required: false,
                            },
                        },
                        {
                            name: "hr_cadre_required",
                            label: "HR Cadre Required",
                            rules: {
                                required: false,
                            },
                        },
                        {
                            name: "lab_test_sites",
                            label: "Lab Test Sites",
                            rules: {
                                required: false,
                            },
                        },
                    ],
                },
            },
        ];
    };

    return {
        currentStep,
        loading,
        departments,
        measureTypes,
        testCatalog,
        stepStatuses,
        validationErrors,
        handleStepChange,
        getValidationErrors,
        handleSpecimenUpdate,
        handleOrganismsUpdate,
        createEmptyMeasure,
        removeNullKeys,
        getSteps,
        prepareDataForSubmission
    };
}