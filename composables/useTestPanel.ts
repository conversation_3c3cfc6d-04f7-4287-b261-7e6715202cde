import { ref, reactive } from 'vue';
import type { TestPanel } from '@/types';
import type { FormErrors, FieldError } from '@/types/input';

export function useTestPanel() {
    const loading = ref<boolean>(false);
    const validationErrors = reactive<Record<string, FieldError>>({});
    const testPanel = reactive<TestPanel>(createEmptyTestPanel());
    function createEmptyTestPanel(): TestPanel {
        return {
            id: 0,
            name: "",
            description: "",
            short_name: "",
            moh_code: null,
            nlims_code: null,
            loinc_code: null,
            preferred_name: null,
            scientific_name: null,
            created_at: "",
            updated_at: "",
            test_types: []
        };
    }

    const handleTestTypesUpdate = (testTypes: number[]) => {
        const safeTestTypes = Array.isArray(testTypes) ? testTypes : [];

        // Check if the arrays are actually different to avoid unnecessary updates
        const currentTestTypes = testPanel.test_types || [];
        if (JSON.stringify([...currentTestTypes].sort()) !== JSON.stringify([...safeTestTypes].sort())) {
            testPanel.test_types = safeTestTypes;
        }

        if (safeTestTypes.length > 0 && validationErrors.test_types) {
            delete validationErrors.test_types;
        }
    };

    const getValidationErrors = (errors: FormErrors) => {
        Object.assign(validationErrors, errors);
    };

    const removeNullKeys = (obj: any) => {
        const newObj = { ...obj };
        Object.keys(newObj).forEach((key) => {
            if (newObj[key] === null || newObj[key] === undefined) {
                delete newObj[key];
            }
        });
        return newObj;
    };

    const prepareDataForSubmission = () => {
        const panelData = { ...testPanel };
        if (panelData.description === null || panelData.description === undefined) {
            panelData.description = '';
        }
        if (!Array.isArray(panelData.test_types)) {
            panelData.test_types = [];
        }

        const cleanedTestPanel = removeNullKeys(panelData);
        return cleanedTestPanel;
    };

    return {
        loading,
        testPanel,
        validationErrors,
        handleTestTypesUpdate,
        getValidationErrors,
        removeNullKeys,
        prepareDataForSubmission,
        createEmptyTestPanel
    };
}
