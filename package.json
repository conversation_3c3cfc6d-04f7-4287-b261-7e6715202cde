{"name": "nuxt-app", "private": true, "type": "module", "version": "v1.0.0", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@iconify/vue": "^4.3.0", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/tailwindcss": "^6.13.2", "@pinia/nuxt": "^0.10.1", "@tailwindcss/postcss": "^4.0.13", "@tailwindcss/vite": "^4.0.7", "@vuepic/vue-datepicker": "^11.0.1", "@vueup/vue-quill": "^1.2.0", "dayjs": "^1.11.13", "nuxt": "^3.15.4", "pinia": "^3.0.1", "vue": "latest", "vue-json-excel3": "^1.0.30", "vue-router": "latest", "vue3-easy-data-table": "^1.5.47", "vue3-toastify": "^0.2.8"}, "devDependencies": {"tailwindcss": "^4.0.7"}}