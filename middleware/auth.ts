import { useAuthStore } from "@/store/auth";

export default defineNuxtRouteMiddleware(async (to, from) => {
  if (import.meta.server) return;
  const authStore = useAuthStore();
  const storedToken = sessionStorage.getItem("auth_token");

  if (!storedToken && !authStore.isAuthenticated) {
    return navigateTo({
      path: "/",
      query: { redirect: to.fullPath },
    });
  }

  if (storedToken && !authStore.isAuthenticated) {
    authStore.setToken(storedToken);
  }

  if (!authStore.isAuthenticated) {
    return navigateTo({
      path: "/",
      query: { redirect: to.fullPath },
    });
  }
});
