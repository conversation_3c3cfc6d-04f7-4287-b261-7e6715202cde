<template>
  <excel
    class="btn btn-default"
    :header="props.exportData.headers"
    :data="exportData.data"
    worksheet="report-work-sheet"
    :name="`${props.exportData.name}.xls`"
  >
    <button
      class="cursor-pointer border flex items-center rounded border-gray-200 px-2.5 py-2 bg-white"
      type="button"
    >
      <Icon icon="vscode-icons:file-type-excel" class="w-5 h-5 mr-1" />
      Export
    </button>
  </excel>
</template>

<script setup lang="ts">
interface ExportData {
  name: string;
  filename?: string;
  headers: string[];
  data: Object[];
  buttonLabel?: string;
}

const props = defineProps({
  exportData: {
    type: Object as PropType<ExportData>,
    required: true,
  },
});
</script>
