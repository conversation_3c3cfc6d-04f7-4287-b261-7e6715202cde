<template>
  <div>
    <Menu as="div" class="relative inline-block text-left">
      <div>
        <MenuButton
          class="p-2 text-gray-600 hover:bg-gray-100 rounded-full border border-gray-100 cursor-pointer transition-colors duration-200"
          title="Quick Actions"
        >
          <Icon icon="heroicons:plus-20-solid" class="h-6 w-6" />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute right-0 mt-2 w-56 origin-top-right bg-white rounded-lg shadow-lg ring-1 ring-gray-200 ring-opacity-5 focus:outline-none z-50"
        >
          <div class="px-2 py-2">
            <div
              class="px-3 py-2 text-lg font-semibold text-gray-500 uppercase tracking-wider"
            >
              Quick Actions
            </div>

            <MenuItem
              v-for="action in quickActions"
              :key="action.id"
              v-slot="{ active }"
            >
              <button
                @click="action.handler"
                :class="[
                  active ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700',
                  'group flex w-full cursor-pointer items-center px-3 py-2 text-base rounded-md transition-colors duration-150',
                ]"
              >
                <div class="mr-3 h-8 w-8 bg-emerald-600 rounded flex items-center justify-center">
                  <Icon :icon="action.icon" class="text-emerald-200 h-5 w-5" />
                </div>
                {{ action.name }}
              </button>
            </MenuItem>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>

<script setup lang="ts">
import { Menu, MenuButton, MenuItems, MenuItem } from "@headlessui/vue";

interface QuickAction {
  id: string;
  name: string;
  icon: string;
  handler: () => void;
}

const quickActions: QuickAction[] = [
  {
    id: "new-test-type",
    name: "New Test Type",
    icon: "healthicons:test-tubes-outline",
    handler: () => navigateTo("/app/test-types/create"),
  },
  {
    id: "new-specimen",
    name: "New Specimen",
    icon: "healthicons:microscope-with-specimen-outline",
    handler: () => navigateTo("/app/specimens/create"),
  },
  {
    id: "new-test-panel",
    name: "New Test Panel",
    icon: "fluent:panel-left-text-20-regular",
    handler: () => navigateTo("/app/test-panels/create"),
  },
  {
    id: "new-drug",
    name: "New Drug",
    icon: "healthicons:medicines-outline",
    handler: () => navigateTo("/app/drugs/create"),
  },
  {
    id: "new-organism",
    name: "New Organism",
    icon: "healthicons:virus-outline",
    handler: () => navigateTo("/app/organisms/create"),
  },
  {
    id: "new-product",
    name: "New Product",
    icon: "eos-icons:products-outlined",
    handler: () => navigateTo("/app/products/create"),
  },
  {
    id: "new-equipment",
    name: "New Equipment",
    icon: "fluent:box-20-regular",
    handler: () => navigateTo("/app/equipments/create"),
  },
  {
    id: "new-department",
    name: "New Department",
    icon: "circum:hospital-1",
    handler: () => navigateTo("/app/departments/create"),
  },
  {
    id: "new-user",
    name: "New Account",
    icon: "mdi:account-multiple-outline",
    handler: () => navigateTo("/app/users/create"),
  },
];
</script>
