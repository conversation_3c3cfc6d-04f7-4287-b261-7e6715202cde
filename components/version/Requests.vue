<template>
  <div class="relative">
    <Popover v-slot="{ open }" class="relative">
      <PopoverButton
        class="relative flex items-center justify-center focus:outline-none cursor-pointer"
        @click="markAllAsRead"
      >
        <Icon
          icon="icon-park-twotone:pull-requests"
          class="h-5 w-5 text-gray-600"
        />
        <div
          v-if="pullRequests.length > 0"
          class="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
          :class="{ '': hasUnreadRequests }"
        >
          {{ pullRequests.length }}
        </div>
      </PopoverButton>

      <transition
        enter-active-class="transition duration-200 ease-out"
        enter-from-class="translate-y-1 opacity-0"
        enter-to-class="translate-y-0 opacity-100"
        leave-active-class="transition duration-150 ease-in"
        leave-from-class="translate-y-0 opacity-100"
        leave-to-class="translate-y-1 opacity-0"
      >
        <PopoverPanel
          class="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded shadow-lg z-50 border border-gray-200 dark:border-gray-700"
        >
          <div
            class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"
          >
            <h3 class="font-medium text-lg">Modification Requests</h3>
            <button
              @click="closePopover"
              class="text-gray-500 hover:text-gray-700"
            >
              <Icon icon="mdi:close" class="h-5 w-5" />
            </button>
          </div>

          <div class="max-h-96 overflow-y-auto">
            <div
              v-if="pullRequests.length === 0"
              class="p-4 text-center text-gray-500"
            >
              No pending pull requests
            </div>

            <div
              v-for="(request, index) in pullRequests"
              :key="index"
              class="p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
            >
              <div class="flex justify-between items-start mb-2">
                <div>
                  <span class="font-medium">{{ request.title }}</span>
                  <p class="text-sm text-gray-500">
                    by {{ request.author }} · {{ request.timeAgo }}
                  </p>
                </div>
                <div class="flex space-x-2">
                  <BaseButton label="Approve" @click="approveRequest(request.id)" scale="s"/>
                </div>
              </div>

              <Disclosure v-slot="{ open }" as="div" class="mt-2 border border-gray-200 rounded">
                <DisclosureButton
                  class="w-full flex justify-between items-center py-2 px-3 bg-gray-100 border-b border-gray-200 dark:bg-gray-700 rounded text-sm font-medium"
                >
                  <span>View Changes</span>
                  <Icon
                    :icon="
                      open ? 'heroicons:chevron-up' : 'heroicons:chevron-down'
                    "
                    class="h-4 w-4"
                  />
                </DisclosureButton>
                <DisclosurePanel
                  class="bg-gray-50 dark:bg-gray-900 rounded-b p-3 text-sm"
                >
                  <div
                    v-for="(change, cIndex) in request.changes"
                    :key="cIndex"
                    class="flex items-start py-1"
                  >
                    <span
                      :class="{
                        'text-green-600': change.type === 'add',
                        'text-red-600': change.type === 'remove',
                        'text-blue-600': change.type === 'modify',
                      }"
                    >
                      <Icon
                        :icon="changeTypeIcon(change.type)"
                        class="h-4 w-4 mr-1 inline-block"
                      />
                    </span>
                    <span>{{ change.description }}</span>
                  </div>
                </DisclosurePanel>
              </Disclosure>
            </div>
          </div>
        </PopoverPanel>
      </transition>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  Popover,
  PopoverButton,
  PopoverPanel,
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/vue";

const pullRequests = ref([
  {
    id: 1,
    title: "Update Test Types Schema",
    author: "John Doe",
    timeAgo: "2h ago",
    unread: true,
    changes: [
      { type: "add", description: "Added new HPV test type" },
      { type: "modify", description: "Updated TB test reference values" },
      { type: "remove", description: "Removed deprecated malaria test" },
    ],
  },
  {
    id: 2,
    title: "Add New Specimen Type",
    author: "Jane Smith",
    timeAgo: "3h ago",
    unread: true,
    changes: [
      { type: "add", description: "Added cerebrospinal fluid specimen type" },
      { type: "add", description: "Added collection procedure documentation" },
    ],
  },
  {
    id: 3,
    title: "Organism Classification Update",
    author: "Robert Chen",
    timeAgo: "1d ago",
    unread: false,
    changes: [
      {
        type: "modify",
        description: "Updated bacterial classification taxonomy",
      },
      { type: "add", description: "Added 3 new resistance markers" },
    ],
  },
]);

const hasUnreadRequests = computed(() => {
  return pullRequests.value.some((request) => request.unread);
});

const markAllAsRead = () => {
  pullRequests.value.forEach((request) => {
    request.unread = false;
  });
};

const closePopover = () => {};

const approveRequest = (id: number) => {
  const index = pullRequests.value.findIndex((req) => req.id === id);
  if (index !== -1) {
    pullRequests.value.splice(index, 1);
  }
};

const changeTypeIcon = (type: string) => {
  switch (type) {
    case "add":
      return "mdi:plus-circle-outline";
    case "remove":
      return "mdi:minus-circle-outline";
    case "modify":
      return "mdi:pencil-outline";
    default:
      return "mdi:information-outline";
  }
};
</script>
