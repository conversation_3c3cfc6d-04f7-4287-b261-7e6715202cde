<template>
  <BaseButton
    label="Release new version"
    icon-end="fluent-mdl2:release-definition"
    @click="isNewReleaseDialogOpen = true"
  />
  <TransitionRoot appear :show="isNewReleaseDialogOpen" as="template">
    <Dialog
      as="div"
      @close="isNewReleaseDialogOpen = false"
      class="relative z-10"
    >
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-2xl transform overflow-hidden rounded bg-white p-6 text-left align-middle shadow-xl transition-all"
            >
              <DialogTitle
                as="h3"
                class="text-lg font-medium leading-6 text-gray-900"
              >
                Create New Release
              </DialogTitle>

              <BaseForm :config="formConfig" @submit="confirmRelease">
                <template v-slot="{ form }">
                  <div class="mt-4 flex flex-col space-y-2.5">
                    <div class="border rounded border-gray-200 p-4">
                      <h4 class="text-md font-medium text-gray-700 mb-3">
                        Release Notes
                      </h4>
                      <div
                        v-for="(note, index) in dataObject.releaseNotes"
                        :key="index"
                        class="mb-4 p-3 border rounded border-gray-200 bg-gray-50"
                      >
                        <div class="flex justify-between items-center mb-2">
                          <h5 class="font-medium text-gray-800">
                            Note #{{ index + 1 }}
                          </h5>
                          <button
                            @click="removeReleaseNote(index)"
                            class="text-red-500 hover:text-red-700 cursor-pointer"
                            type="button"
                          >
                            <Icon icon="carbon:close-filled" class="h-5 w-5" />
                          </button>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <div class="md:col-span-3">
                            <BaseInput
                              label="Title"
                              name="title"
                              v-model="note.title"
                              placeholder="Feature title"
                              required
                            />
                          </div>
                          <div class="md:col-span-3">
                            <BaseInput
                              label="Description"
                              name="description"
                              v-model="note.description"
                              placeholder="Describe the change..."
                              type="textarea"
                              required
                            />
                          </div>
                          <div>
                            <BaseDropdown
                              label="Change Type"
                              name="changeType"
                              v-model="note.changeType"
                              :options="changeTypes"
                              required
                            />
                          </div>
                        </div>
                      </div>

                      <BaseButton
                        label="Add Release Note"
                        icon-start="fluent-mdl2:add"
                        @click="addReleaseNote"
                        class="mt-3"
                      />
                    </div>
                  </div>

                  <div class="mt-6 flex justify-end space-x-3">
                    <BaseButton
                      label="Cancel"
                      @click="isNewReleaseDialogOpen = false"
                      appearance="outline"
                      kind="neutral"
                    />
                    <BaseButton
                      label="Reset"
                      appearance="outline"
                      kind="neutral"
                      @click="resetForm"
                    />
                    <BaseButton
                      label="Create Release"
                      appearance="solid"
                      kind="brand"
                      type="submit"
                    />
                  </div>
                </template>
              </BaseForm>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import type { FormConfig } from "@/types/input";

interface ReleaseNote {
  title: string;
  description: string;
  changeType: {
    id: string;
    label: string;
  } | null;
}

interface ReleaseData {
  releaseNotes: ReleaseNote[];
}

const emits = defineEmits<{
  (e: "close"): void;
  (e: "confirm", data: ReleaseData): void;
}>();

const isNewReleaseDialogOpen = ref<boolean>(false);
const { $toast } = useNuxtApp();

const dataObject = ref<ReleaseData>({
  releaseNotes: [
    {
      title: "",
      description: "",
      changeType: null,
    },
  ],
});

const formConfig: FormConfig = {
  fields: [
    {
      name: "title",
      rules: {
        required: true,
      },
    },
    {
      name: "description",
      rules: {
        required: true,
      },
    },
    {
      name: "changeType",
      rules: {
        required: true,
      },
    },
  ],
};

const changeTypes = [
  {
    label: "Feature",
    id: "feature",
  },
  {
    label: "Enhancement",
    id: "enhancement",
  },
  {
    label: "Bug Fix",
    id: "bug-fix",
  },
  {
    label: "Security",
    id: "security",
  },
  {
    label: "Documentation",
    id: "documentation",
  },
  {
    label: "Performance",
    id: "performance",
  },
];

const addReleaseNote = (): void => {
  dataObject.value.releaseNotes.push({
    title: "",
    description: "",
    changeType: null,
  });
};

const removeReleaseNote = (index: number) => {
  if (dataObject.value.releaseNotes.length > 1) {
    dataObject.value.releaseNotes.splice(index, 1);
  }
};

const resetForm = (): void => {
  dataObject.value = {
    releaseNotes: [
      {
        title: "",
        description: "",
        changeType: null,
      },
    ],
  };
};

const confirmRelease = (callbackValues: any, isValid: boolean, errors: any) => {
  if (errors) {
    $toast.error("Please fill in all required fields.");
    return;
  }
  if (!isValid) return;
  emits("confirm", dataObject.value);
  isNewReleaseDialogOpen.value = false;
  resetForm();
};
</script>
