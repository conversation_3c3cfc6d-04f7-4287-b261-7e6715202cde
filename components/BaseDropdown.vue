<template>
  <div class="w-full relative flex flex-col gap-1.5">
    <label v-if="label" :for="id" class="block text-base font-medium text-gray-700">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <div v-if="isMultiple && selectedOptions.length" class="flex flex-wrap gap-2 mb-1">
      <div v-for="(option, index) in selectedOptions" :key="option.id"
        class="flex items-center bg-emerald-100 text-emerald-700 px-2 py-1 rounded">
        {{ option.label }}
        <button @click.stop="removeOption(index)" class="ml-2 text-emerald-600 focus:outline-none">
          <Icon icon="carbon:close-filled" class="w-4 h-4" />
        </button>
      </div>
    </div>

    <div class="relative">
      <input type="text" v-model="searchQuery" @focus="handleFocus" @blur="handleBlur" @input="handleInput"
        @keydown.down.prevent="navigateOptions('down')" @keydown.up.prevent="navigateOptions('up')"
        @keydown.enter.prevent="selectCurrentOption" @keydown.esc="isOpen = false" :placeholder="placeholder" :id="id"
        :aria-invalid="!!error" :aria-errormessage="`error-${id}`"
        class="w-full px-2 py-1 border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
        :class="{
          'border-red-500 focus:ring-red-500 focus:border-red-500': error,
        }" />

      <button type="button" @click="toggleDropdown"
        class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 focus:outline-none">
        <svg class="w-5 h-5 transition-transform duration-200" :class="{ 'rotate-180': isOpen }"
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd" />
        </svg>
      </button>
      <div v-if="loading" class="absolute right-2 top-1/2 -translate-y-1/2">
        <div class="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-emerald-500" />
      </div>
    </div>

    <div v-if="errorToDisplay" class="flex items-center mt-1" :id="`error-${id}`">
      <Icon icon="circum:warning" class="w-4 h-4 text-red-600 mr-2" />
      <p class="text-sm text-red-600">{{ errorToDisplay }}</p>
    </div>

    <div v-if="isOpen" class="absolute z-10 w-full bg-white rounded-md shadow-lg max-h-60 overflow-auto mt-1"
      style="top: 55%; margin-top: 2rem">
      <ul class="py-1">
        <li v-if="filteredOptions.length === 0" class="px-4 py-2 text-gray-500">
          No results found
        </li>
        <li v-else-if="!hasActiveOption" class="px-4 py-2 text-emerald-600 bg-emerald-50 font-medium">
          Select an option
        </li>
        <li v-for="(option, index) in filteredOptions" :key="option.id" @mousedown.prevent="selectOption(index)"
          @mouseover="activeOptionIndex = index"
          class="px-4 py-2 cursor-pointer hover:bg-emerald-50 transition-colors duration-150 flex items-center"
          :class="{ 'bg-emerald-100': index === activeOptionIndex }">
          <span>{{ option.label }}</span>

          <span v-if="
            isMultiple ? isSelected(option) : option.id === selectedOption?.id
          " class="ml-auto text-emerald-600 flex items-center space-x-2">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch, inject } from "vue";
import type { FieldError, FormContext, ValidationRules } from "@/types/input";
import type { Option } from "@/types/form";

export default defineComponent({
  name: "DropdownAutocomplete",
  props: {
    name: {
      required: true,
      type: String,
    },
    options: {
      type: Array as () => Option[],
      required: true,
    },
    modelValue: {
      type: [String, Number, Object, Array] as PropType<
        string | number | { id: string; label: string } | any[] | null
      >,
      default: null,
    },
    isMultiple: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "Select an option",
    },
    id: {
      type: String,
      default: () => `dropdown-${Math.random().toString(36).substr(2, 9)}`,
    },
    required: {
      type: Boolean,
      required: false,
      default: false,
    },
    label: {
      type: String,
      required: false,
    },
    errorMessage: {
      type: String,
      required: false,
    },
    validateOnBlur: {
      type: Boolean,
      default: true,
    },
    validateOnChange: {
      type: Boolean,
      default: false,
    },
    validateOnMount: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Object as () => ValidationRules,
      required: false,
    },
    hideNotSelected: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue", "change", "validated", "blur", "focus"],
  setup(props, { emit }) {
    const isOpen = ref<boolean>(false);
    const searchQuery = ref<string>("");
    const activeOptionIndex = ref<number>(-1);
    const selectedOption = ref<Option | null>(null);
    const selectedOptions = ref<Option[]>([]);
    const error = ref<FieldError | undefined>(undefined);
    const touched = ref(false);
    const formContext = inject<FormContext | null>("formContext", null);

    const hasActiveOption = computed(
      () =>
        activeOptionIndex.value >= 0 &&
        activeOptionIndex.value < filteredOptions.value.length
    );

    const initializeSelection = () => {
      if (props.modelValue !== 0 && props.modelValue !== "") {
        if (props.isMultiple && Array.isArray(props.modelValue)) {
          const modelValueIds = props.modelValue.map((id) => String(id));
          selectedOptions.value = props.options.filter((opt) =>
            modelValueIds.includes(String(opt.id))
          );
        } else if (!props.isMultiple) {
          const found = props.options.find(
            (opt) => String(opt.id) === String(props.modelValue)
          );
          if (found) {
            selectedOption.value = found;
            searchQuery.value = found.label;
          }
        }
      }

      if (formContext && formContext.formValues[props.name] !== undefined) {
        if (
          props.isMultiple &&
          Array.isArray(formContext.formValues[props.name])
        ) {
          const formValueIds = formContext.formValues[props.name].map(
            (id: any) => String(id)
          );
          selectedOptions.value = props.options.filter((opt) =>
            formValueIds.includes(String(opt.id))
          );
        } else if (!props.isMultiple) {
          const found = props.options.find(
            (opt) =>
              String(opt.id) === String(formContext.formValues[props.name])
          );
          if (found) {
            selectedOption.value = found;
            searchQuery.value = found.label;
          }
        }
      }
    };

    onMounted((): void => {
      initializeSelection();
      if (props.validateOnMount) {
        validate();
      }
    });

    watch(
      () => props.options,
      () => {
        initializeSelection();
      },
      { deep: true }
    );

    const filteredOptions = computed<Option[]>(() => {
      let filtered = props.options;

      if (searchQuery.value) {
        filtered = filtered.filter((option) =>
          option.label.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
      }

      if (props.hideNotSelected) {
        const selectedIds = selectedOptions.value.map((opt) => String(opt.id));
        filtered = filtered.filter(
          (option) => !selectedIds.includes(String(option.id))
        );
      }

      return filtered;
    });

    const toggleDropdown = (): void => {
      isOpen.value = !isOpen.value;
      if (isOpen.value) activeOptionIndex.value = -1;
    };

    const closeDropdown = (): void => {
      setTimeout(() => {
        isOpen.value = false;
        if (props.isMultiple) {
          searchQuery.value = "";
        } else if (!selectedOption.value) {
          searchQuery.value = "";
        }
        activeOptionIndex.value = -1;
      }, 150);
    };

    const handleInput = (): void => {
      isOpen.value = true;
    };

    const handleBlur = () => {
      touched.value = true;
      emit("blur");
      if (props.validateOnBlur) {
        validate();
      }
      closeDropdown();
    };

    const handleFocus = () => {
      isOpen.value = true;
      emit("focus");
      validate();
    };

    const navigateOptions = (direction: "up" | "down") => {
      if (!isOpen.value) return (isOpen.value = true);
      if (filteredOptions.value.length === 0) return;

      if (direction === "down") {
        if (activeOptionIndex.value < filteredOptions.value.length - 1) {
          activeOptionIndex.value++;
        } else {
          activeOptionIndex.value = 0;
        }
      } else {
        if (activeOptionIndex.value > 0) {
          activeOptionIndex.value--;
        } else {
          activeOptionIndex.value = filteredOptions.value.length - 1;
        }
      }
    };

    const selectCurrentOption = () => {
      if (hasActiveOption.value) {
        selectOption(activeOptionIndex.value);
      }
    };

    const validate = () => {
      let fieldError: FieldError | undefined = undefined;

      if (props.required) {
        if (props.isMultiple && selectedOptions.value.length === 0) {
          fieldError = {
            type: "required",
            message: props.errorMessage || "This field is required",
          };
        } else if (!props.isMultiple && !selectedOption.value) {
          fieldError = {
            type: "required",
            message: props.errorMessage || "This field is required",
          };
        }
      }

      error.value = fieldError;
      emit("validated", fieldError);
      return !error.value;
    };

    const selectOption = (index: number): void => {
      if (index < 0 || index >= filteredOptions.value.length) return;
      const option = filteredOptions.value[index];

      if (props.isMultiple) {
        if (!selectedOptions.value.some((opt) => opt.id === option.id)) {
          selectedOptions.value.push(option);
        }
        searchQuery.value = "";
        emit(
          "update:modelValue",
          selectedOptions.value.map((opt) => opt.id)
        );
      } else {
        selectedOption.value = option;
        searchQuery.value = option.label;
        emit("update:modelValue", option.id);
      }

      if (error.value?.type === "required") {
        error.value = undefined;
        emit("validated", undefined);
      }

      emit("change", option);

      if (!props.isMultiple) {
        isOpen.value = false;
      }

      activeOptionIndex.value = -1;
    };

    const removeOption = (index: number): void => {
      selectedOptions.value.splice(index, 1);
      emit(
        "update:modelValue",
        selectedOptions.value.map((opt) => opt.id)
      );

      if (props.required && selectedOptions.value.length === 0) {
        error.value = {
          type: "required",
          message: props.errorMessage || "This field is required",
        };
        emit("validated", error.value);
      } else if (props.validateOnChange) {
        validate();
      }
    };

    const isSelected = (option: Option): boolean =>
      selectedOptions.value.some((opt) => String(opt.id) === String(option.id));

    const errorToDisplay = computed(() => {
      return error.value?.message || "";
    });

    watch(
      () => props.modelValue,
      (newValue) => {
        if (newValue !== null) {
          if (props.isMultiple && Array.isArray(newValue)) {
            const modelValueIds = newValue.map((id) => String(id));
            selectedOptions.value = props.options.filter((opt) =>
              modelValueIds.includes(String(opt.id))
            );
          } else if (!props.isMultiple) {
            const found = props.options.find(
              (opt) => String(opt.id) === String(newValue)
            );
            if (found) {
              selectedOption.value = found;
              searchQuery.value = found.label;
            }
          }
        } else {
          if (props.isMultiple) {
            selectedOptions.value = [];
          } else {
            selectedOption.value = null;
            searchQuery.value = "";
          }
        }

        if (formContext) {
          formContext.formValues[props.name] = newValue;
        }

        if (error.value?.type === "required") {
          if (
            (props.isMultiple &&
              Array.isArray(newValue) &&
              newValue.length > 0) ||
            (!props.isMultiple && newValue !== null && newValue !== undefined)
          ) {
            error.value = undefined;
            emit("validated", undefined);
          }
        }
      },
      { deep: true }
    );

    watch(
      () => formContext?.formErrors[props.name],
      (newError) => {
        error.value = newError;
      },
      { immediate: true }
    );

    watch(
      () => props.errorMessage,
      (newMessage) => {
        if (newMessage && !error.value) {
          error.value = {
            type: "custom",
            message: newMessage,
          };
        } else if (!newMessage && error.value?.type === "custom") {
          error.value = undefined;
        }
      },
      { immediate: true }
    );

    watch(
      () => selectedOption.value,
      (newVal) => {
        if (newVal && error.value?.type === "required") {
          error.value = undefined;
          emit("validated", undefined);
        }
      }
    );

    watch(
      () => selectedOptions.value.length,
      (newLength) => {
        if (newLength > 0 && error.value?.type === "required") {
          error.value = undefined;
          emit("validated", undefined);
        }
      }
    );

    return {
      isOpen,
      error,
      searchQuery,
      activeOptionIndex,
      selectedOption,
      selectedOptions,
      filteredOptions,
      errorToDisplay,
      toggleDropdown,
      closeDropdown,
      handleInput,
      navigateOptions,
      selectOption,
      removeOption,
      isSelected,
      handleBlur,
      handleFocus,
      validate,
      hasActiveOption,
      selectCurrentOption,
    };
  },
});
</script>
