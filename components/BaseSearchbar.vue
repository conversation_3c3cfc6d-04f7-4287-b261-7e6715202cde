<script setup lang="ts">
import { ref, defineEmits, defineProps } from "vue";
import { Icon } from "@iconify/vue";

const props = defineProps<{
    modelValue: string;
    placeholder?: string;
    clearable?: boolean;
}>();

const emit = defineEmits(["update:modelValue", "search"]);

const searchQuery = ref(props.modelValue);

const updateSearch = (event: Event) => {
    const value = (event.target as HTMLInputElement).value;
    searchQuery.value = value;
    emit("update:modelValue", value);
    emit("search", value);
};

const clearSearch = () => {
    searchQuery.value = "";
    emit("update:modelValue", "");
    emit("search", "");
};
</script>

<template>
    <div class="relative bg-white rounded border border-gray-200 w-full">
        <input
            :value="searchQuery"
            @input="updateSearch"
            :placeholder="placeholder || 'Search...'"
            class="w-full focus:outline-none focus:ring-0 px-10 py-1.5"
        />
        <div class="absolute top-0 left-0 bg-emerald-600 rounded-l py-2 px-2">
            <Icon icon="mynaui:search" class="h-5 w-5 text-emerald-100" />
        </div>
        <div v-if="props.clearable && searchQuery" class="absolute top-2 right-2 cursor-pointer" @click="clearSearch">
            <Icon icon="mdi:close-circle" class="h-5 w-5 text-zinc-500" />
        </div>
    </div>
</template>
