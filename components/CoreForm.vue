<script setup lang="ts">
import { provide, watch } from 'vue';
import { useFormValidation } from './validators/form';
import type { FormConfig } from '@/types/input';

interface Props {
    config: FormConfig;
    initialValues?: Record<string, any>;
    validateOnSubmit?: boolean;
    validateOnChange?: boolean;
    validateOnMount?: boolean;
    modelValue?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
    validateOnSubmit: true,
    validateOnChange: false,
    validateOnMount: false
});

const emit = defineEmits<{
    (e: 'submit', values: Record<string, any>, valid: boolean, errors?: any): void;
    (e: 'validation', valid: boolean, errors: Record<string, any>): void;
    (e: 'reset'): void;
    (e: 'update:modelValue', values: Record<string, any>): void;
}>();

const {
    formValues,
    formErrors,
    isValid,
    validateField,
    validateAll,
    resetForm
} = useFormValidation(props.config);

if (props.initialValues) {
    Object.entries(props.initialValues).forEach(([key, value]) => {
        if (key in formValues) {
            formValues[key] = value;
        }
    });
}

if (props.modelValue) {
    Object.entries(props.modelValue).forEach(([key, value]) => {
        if (key in formValues) {
            formValues[key] = value;
        }
    });
}

watch(formValues, (newValues) => {
    if (props.modelValue) {
        emit('update:modelValue', { ...newValues });
    }
    
    if (props.validateOnChange) {
        validateAll();
    }
}, { deep: true });

watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        Object.entries(newValue).forEach(([key, value]) => {
            if (key in formValues && formValues[key] !== value) {
                formValues[key] = value;
            }
        });
    }
}, { deep: true });

provide('formContext', {
    formValues,
    formErrors,
    isValid,
    validateField,
    validateAll,
    resetForm
});

const handleSubmit = (e: Event) => {
    e.preventDefault();

    if (props.modelValue) {
        Object.keys(formValues).forEach(key => {
            if (key in props.modelValue!) {
                (props.modelValue as any)[key] = formValues[key];
            }
        });
    }

    if (props.validateOnSubmit) {
        const validationResult = validateAll();
        emit('validation', validationResult.valid, validationResult.errors);

        if (validationResult.valid) {
            emit('submit', { ...formValues }, true);
        } else {
            emit('submit', { ...formValues }, false, {...formErrors});
        }
    } else {
        emit('submit', { ...formValues }, isValid.value, {...formErrors});
    }
};

const handleReset = () => {
    resetForm();
    emit('reset');
};
</script>

<template>
    <form @submit="handleSubmit" @reset="handleReset" novalidate>
        <slot :form="{ values: formValues, errors: formErrors, isValid }"></slot>
    </form>
</template>
