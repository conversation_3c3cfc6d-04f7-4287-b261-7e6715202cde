<script setup lang="ts">
import { ref, computed, watch, type PropType } from 'vue';
import type { MeasureType, Parameter } from '@/types';

const formData = ref<Record<string, any>>({});

const props = defineProps({
    measureTypes: {
        type: Array as PropType<MeasureType[]>,
        required: true
    },
    modelValue: {
        type: Object as PropType<MeasureType>,
        required: true
    },
    initialValues: {
        type: Object as PropType<Record<string, any>>,
        default: () => ({})
    },
    instanceId: {
        type: [String, Number],
        default: () => Date.now().toString() + Math.random().toString(36).substring(2, 9)
    }
});

watch([() => props.initialValues, () => props.instanceId], ([newValues]) => {
    formData.value = {};
    if (newValues && Object.keys(newValues).length > 0) {
        formData.value = { ...newValues };
    }
}, { immediate: true });

const selectedMeasureType = computed(() => {
    if (!props.modelValue) return null;
    return props.measureTypes.find(type => type.id === props.modelValue.id) || null;
});

const emit = defineEmits(['update:onValueChange']);

const updateData = (): void => {
    emit('update:onValueChange', { ...formData.value });
}

watch(formData, () => {
    emit('update:onValueChange', formData.value);
}, { deep: true });

const renderInputForParameter = (parameter: Parameter, paramKey: string, rangeIndex: number): void => {
    if (!formData.value[rangeIndex]) {
        formData.value[rangeIndex] = { value: '', interpretation: '' };
    }

    if (parameter.type === 'range') {
        formData.value[`${paramKey}_min`] = formData.value[`${paramKey}_min`] || '';
        formData.value[`${paramKey}_max`] = formData.value[`${paramKey}_max`] || '';
    } else if (parameter.type === 'options' && Array.isArray(parameter.values)) {
        formData.value[paramKey] = formData.value[paramKey] || parameter.values[0] || '';
    }

    updateData();
};

</script>

<template>
    <div class="w-full">
        <div v-if="selectedMeasureType">
            <template v-if="selectedMeasureType.structure?.parameters">
                <div class="w-full grid grid-cols-2 gap-5"
                    v-for="(parameter, index) in selectedMeasureType.structure.parameters"
                    :key="`${props.instanceId}_${index}`">
                    <div v-if="parameter.type === 'range'" v-bind:key="`${props.instanceId}_${index}`">
                        <h3 class="py-4 text-base font-medium text-gray-700">{{ parameter.name }}</h3>
                        <div class="w-full grid grid-cols-2 gap-5">
                            <BaseInput type="number" :name="`${parameter.name}_min_${props.instanceId}_${index}`"
                                v-model="formData[`${parameter.name}_min_${props.instanceId}_${index}`]"
                                @input="renderInputForParameter(parameter, `${parameter.name}_min_${props.instanceId}_${index}`, index)"
                                placeholder="Min" />
                            <BaseInput type="number" :name="`${parameter.name}_max_${props.instanceId}_${index}`"
                                v-model="formData[`${parameter.name}_max_${props.instanceId}_${index}`]"
                                @input="renderInputForParameter(parameter, `${parameter.name}_max_${props.instanceId}_${index}`, index)"
                                placeholder="Max" />
                        </div>
                    </div>

                    <div v-else-if="parameter.type === 'options' && Array.isArray(parameter.values)" class="mt-2">
                        <BaseDropdown :label="capitalize(parameter.name)" :name="`options_${props.instanceId}_${index}`"
                            :options="parameter.values.map((param, optIndex) => { return { id: optIndex + 1, label: String(param) } })"
                            :modelValue="formData[`${parameter.name}_${props.instanceId}_${index}`]" />
                    </div>

                    <div v-else-if="parameter.type === 'string'" class="mt-2">
                        <BaseInput label="Value" type="text" :name="`value_${props.instanceId}_${index}`"
                            v-model="formData[index].value" />
                        <BaseInput label="Interpretation" type="text"
                            :name="`interpretation_${props.instanceId}_${index}`"
                            v-model="formData[index].interpretation" />
                    </div>
                </div>
            </template>
        </div>
        <div v-else>
            <div>
                <p>No measure type selected.</p>
            </div>
        </div>
    </div>
</template>