<template>
    <BaseForm :config="currentStepObj!.validationSchema" @submit="onSubmitForm">
        <template v-slot="">
            <div class="stepper-container">
                <ol
                    class="flex items-center w-full p-3 space-x-2 text-sm font-medium text-center text-gray-500 bg-white dark:text-gray-400 sm:text-base dark:bg-gray-800 dark:border-gray-700 sm:p-4 sm:space-x-4 rtl:space-x-reverse">
                    <li v-for="step in steps" :key="step.id" class="flex items-center cursor-pointer" :class="{
                        'text-emerald-600': getStepStatus(step.id) === 'success',
                        'text-red-600': getStepStatus(step.id) === 'error',
                        'text-gray-500': getStepStatus(step.id) === 'neutral',
                        'cursor-not-allowed opacity-50': !isStepClickable(step.id)
                    }" @click="handleStepClick(step.id)">
                        <span class="flex items-center justify-center w-5 h-5 me-2 text-xs border rounded-full shrink-0"
                            :class="{
                                'border-emerald-600 bg-emerald-600 text-white': getStepStatus(step.id) === 'success',
                                'border-red-600 bg-red-600 text-white': getStepStatus(step.id) === 'error',
                                'border-gray-500 bg-white text-gray-500': getStepStatus(step.id) === 'neutral'
                            }">
                            <template v-if="getStepStatus(step.id) === 'error'">
                                <svg class="w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" width="1024"
                                    height="1024" viewBox="0 0 1024 1024">
                                    <path fill="currentColor"
                                        d="M448 804a64 64 0 1 0 128 0a64 64 0 1 0-128 0m32-168h64c4.4 0 8-3.6 8-8V164c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8" />
                                </svg>
                            </template>
                            <template v-else>
                                {{ step.id }}
                            </template>
                        </span>
                        <span>{{ step.title }}</span>
                        <svg v-if="step.id < steps.length" class="w-3 h-3 ms-2 sm:ms-4 rtl:rotate-180"
                            aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m7 9 4-4-4-4M1 9l4-4-4-4" />
                        </svg>
                    </li>
                </ol>

                <div class="step-content p-5">
                    <slot name="default" :current-step="currentStep"></slot>
                </div>

                <div class="flex justify-between mt-6 p-5">
                    <button v-if="currentStep > 1" type="button" @click="prevStep"
                        class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 focus:outline-none focus:ring-none focus:ring-gray-500 focus:ring-offset-2">
                        Previous
                    </button>
                    <div v-else></div>

                    <BaseButton v-if="currentStep < steps.length" label="Validate & Next" type="button"
                        @click="nextStep" class="mr-2" />

                    <BaseButton v-if="currentStep === steps.length" label="Submit" type="submit" :loading="submitting"
                        />
                </div>
            </div>
        </template>
    </BaseForm>
</template>

<script setup lang="ts">
import type { Step } from '@/types/form';
import type { ValidationResult } from '@/types/input';
import { useFormValidation } from './validators/form';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    steps: {
        type: Array as () => Step[],
        required: true,
        validator: (value: Step[]) => {
            return value.every((step: Step) => 'id' in step && 'title' in step);
        }
    },
    initialStep: {
        type: Number,
        default: 1
    },
    allowSkip: {
        type: Boolean,
        default: false
    },
    stepStatuses: {
        type: Object,
        default: () => ({})
    },
    loadingComponent: {
        required: false,
        type: Boolean
    },
    submitting: {
        required: false,
        type: Boolean
    }
});

const emit = defineEmits(['before-next-step', 'step-changed', 'submit', 'validate', 'validation-errors']);

const { $toast } = useNuxtApp();
const currentStep = ref<number>(props.initialStep);
const completedSteps = ref<Set<number>>(new Set<number>());
const errorSteps = ref<Set<number>>(new Set<number>());


const currentStepObj = computed(() => {
    return props.steps.find((step: Step) => step.id === currentStep.value);
});

const { formValues: stepValues, formErrors, isValid, validateForm } = useFormValidation(currentStepObj.value!.validationSchema);

const validateCurrentStep = async (): Promise<ValidationResult> => {
    const result = validateForm(stepValues, currentStepObj.value!.validationSchema);
    return result;
};

const updateStepValidationStatus = (step: number, valid: boolean): void => {
    props.steps.map((item: Step) => {
        if (item.id === step) {
            emit('validate', { ...item, filled: true, valid })
            return { ...item, filled: true, valid };
        }
        return item;
    });
};

const nextStep = async (): Promise<void> => {
    if (currentStep.value < props.steps.length) {
        const validationResult = validateCurrentStep();
        updateStepValidationStatus(currentStep.value, (await validationResult).valid);
        if ((await validationResult).valid) {
            markStepComplete(currentStep.value);
            currentStep.value++;
        } else {
            if (!(await validationResult).valid) {
                markStepError(currentStep.value);
                const flattenErrors = (errors: Record<string, any>, parentKey = '') => {
                    Object.entries(errors).forEach(([key, value]) => {
                        const field = parentKey ? `${parentKey}.${key}` : key;
                        if (typeof value === 'object' && value !== null && 'message' in value) {
                            $toast.error(`${field.toString().replaceAll("_", " ")}: ${value.message}`);
                        } else if (typeof value === 'object') {
                            flattenErrors(value, field);
                        } else {
                            $toast.error(`${field}: ${value}`);
                        }
                    });
                };
                const validationErrors = (await validationResult).errors
                flattenErrors(validationErrors);
                emit('validation-errors', validationErrors);
            }
        }
    }
}

const prevStep = (): void => {
    if (currentStep.value > 1) {
        currentStep.value--;
        emit('step-changed', currentStep.value);
    }
}

const goToStep = (stepNumber: number): void => {
    if (isStepClickable(stepNumber)) {
        currentStep.value = stepNumber;
        emit('step-changed', currentStep.value);
    }
}

const handleStepClick = (stepNumber: number): void => {
    if (isStepClickable(stepNumber)) {
        goToStep(stepNumber);
    }
}

const isStepClickable = (stepNumber: number): boolean => {
    if (props.allowSkip) return true;
    if (stepNumber < currentStep.value) return true;
    if (stepNumber === currentStep.value) return true;
    if (stepNumber === currentStep.value + 1 && completedSteps.value.has(currentStep.value)) return true;
    return false;
}

const updateCompletedSteps = (statuses: Record<string, string>): void => {
    for (const [step, status] of Object.entries(statuses)) {
        const stepNumber = parseInt(step);
        if (status === 'completed' || status === 'success') {
            completedSteps.value.add(stepNumber);
            errorSteps.value.delete(stepNumber);
        } else if (status === 'error') {
            errorSteps.value.add(stepNumber);
            completedSteps.value.delete(stepNumber);
        }
    }
}


const getStepStatus = (stepNumber: number): 'success' | 'error' | 'neutral' => {
    if (props.stepStatuses[stepNumber]) {
        if (props.stepStatuses[stepNumber] === 'error') return 'error';
        if (props.stepStatuses[stepNumber] === 'success' || props.stepStatuses[stepNumber] === 'completed') return 'success';
    }

    if (errorSteps.value.has(stepNumber)) return 'error';
    if (completedSteps.value.has(stepNumber)) return 'success';

    return 'neutral';
}

const markStepComplete = (stepNumber: number): void => {
    completedSteps.value.add(stepNumber);
    errorSteps.value.delete(stepNumber);
}

const markStepError = (stepNumber: number): void => {
    errorSteps.value.add(stepNumber);
    completedSteps.value.delete(stepNumber);
}

const resetStepStatus = (stepNumber: number): void => {
    completedSteps.value.delete(stepNumber);
    errorSteps.value.delete(stepNumber);
}

const onSubmitForm = (values: any): void => {
    emit('submit', {
        data: values
    });
}

watch(() => props.stepStatuses, (newStatuses) => {
    updateCompletedSteps(newStatuses);
}, { deep: true });

provide('formContext', {
    stepValues: computed(() => stepValues),
    formErrors: computed(() => formErrors),
    isValid: computed(() => isValid),
    validateCurrentStep,
    onSubmitForm
});

defineExpose({
    nextStep,
    prevStep,
    goToStep,
    markStepComplete,
    markStepError,
    resetStepStatus,
    getStepStatus
});

watch(
    () => props.data,
    (newData) => {
        if (newData?.test_type) {
            if (stepValues) {
                Object.assign(stepValues, {
                    test_type_name: newData.test_type.name,
                    short_name: newData.test_type.short_name,
                    preferred_name: newData.test_type.preferred_name,
                    scientific_name: newData.test_type.scientific_name,
                    lonic_code: newData.test_type.loinc_code,
                    moh_code: newData.test_type.moh_code,
                    target_tat: newData.test_type.targetTAT,
                    iblis_mapping_name: newData.test_type.iblis_mapping_name,
                    test_category_id: newData.test_type.test_category_id,
                    can_be_done_on_sex: newData.test_type.can_be_done_on_sex,
                    specimen_types: newData.specimen_types,
                    measures: newData.measures,
                    organisms: newData.organisms,
                    lab_test_sites: newData.lab_test_sites,
                    assay_format: newData.test_type.assay_format,
                    hr_cadre_required: newData.test_type.hr_cadre_required,
                    equipment: newData.equipment,
                });
            }
        }
    },
    { deep: true, immediate: true }
);

</script>

<style scoped>
.stepper-container {
    width: 100%;
}
</style>