<template>
  <div>
    <div class="mb-8">
      <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3">
        <h2 class="text-xl font-semibold">Basic Information</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
        <BaseInput
          label="Name"
          name="name"
          v-model="testPanel.name"
          v-bind:error-message="errors.name?.message"
          required
        />

        <BaseInput
          label="Short Name"
          name="short_name"
          v-model="testPanel.short_name"
          v-bind:error-message="errors.short_name?.message"
          required
        />

        <BaseInput
          label="Preferred Name"
          name="preferred_name"
          v-model="testPanel.preferred_name"
          v-bind:error-message="errors.preferred_name?.message"
        />

        <BaseInput
          label="Scientific Name"
          name="scientific_name"
          v-model="testPanel.scientific_name"
          v-bind:error-message="errors.scientific_name?.message"
        />

        <BaseInput
          label="LOINC Code"
          name="loinc_code"
          v-model="testPanel.loinc_code"
          v-bind:error-message="errors.loinc_code?.message"
          icon-start="bi:qr-code"
        />

        <BaseInput
          label="NLIMS Code"
          name="nlims_code"
          v-model="testPanel.nlims_code"
          v-bind:error-message="errors.nlims_code?.message"
          icon-start="bi:qr-code"
        />

        <BaseInput
          label="MOH Code"
          name="moh_code"
          v-model="testPanel.moh_code"
          v-bind:error-message="errors.moh_code?.message"
          icon-start="bi:qr-code"
        />

        <div class="col-span-2">
          <label for="description" class="block text-base font-medium text-gray-700 mb-1">
            Description <span class="text-red-500">*</span>
          </label>
          <div class="overflow-hidden">
            <RichTextEditor
              theme="snow"
              class="editor"
              v-model:content="props.testPanel.description"
              contentType="html"
              @update:content="updateDescription"
            ></RichTextEditor>
            <div v-if="errors.description?.message" class="text-red-500 text-sm mt-1">
              {{ errors.description.message }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, type PropType, ref, watch } from 'vue';
import type { TestPanel } from '@/types';
import type { FieldError } from '@/types/input';

const props = defineProps({
  testPanel: {
    required: true,
    type: Object as PropType<TestPanel>
  },
  errors: {
    required: true,
    type: Object as PropType<Record<string, FieldError>>
  }
});

const updateDescription = (content: string) => {
  // Ensure content is never null, use empty string as fallback
  const safeContent = content || '';
  props.testPanel.description = safeContent;

  // Log the updated description for debugging
  console.log('Description updated:', props.testPanel.description);

  // Emit an event to notify parent component about the description update
  // This helps with form validation
  const event = new CustomEvent('description-updated', {
    detail: { description: safeContent }
  });
  window.dispatchEvent(event);
};
</script>

<style scoped></style>
