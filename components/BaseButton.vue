<script setup lang="ts">
interface Props {
    label?: string;
    disabled?: boolean;
    loading?: boolean;
    round?: boolean;
    iconStart?: string;
    iconEnd?: string;
    alignment?: 'start' | 'center' | 'end';
    appearance?: 'solid' | 'outline' | 'transparent';
    kind?: 'brand' | 'neutral' | 'danger' | 'inverse';
    scale?: 's' | 'm' | 'l';
    fullWidth?: boolean;
    type?: 'button' | 'submit' | 'reset';
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    loading: false,
    round: false,
    alignment: 'center',
    appearance: 'solid',
    kind: 'brand',
    scale: 'm',
    fullWidth: false,
    type: 'button'
});

const baseClasses = computed(() => ({
    'inline-flex py-2 items-center justify-center cursor-pointer transition-colors duration-200': true,
    'w-full': props.fullWidth,
    'w-auto': !props.fullWidth,
    'justify-start': props.alignment === 'start',
    'justify-center': props.alignment === 'center',
    'justify-end': props.alignment === 'end',
    'rounded-full': props.round,
    'rounded': !props.round,
    'px-3 py-1.5 text-sm': props.scale === 's',
    'px-4 py-2 text-base': props.scale === 'm',
    'px-6 py-3 text-lg': props.scale === 'l',
    'opacity-50 cursor-not-allowed': props.disabled,
    'bg-emerald-600 text-white hover:bg-emerald-700': props.appearance === 'solid' && props.kind === 'brand' && !props.disabled,
    'bg-gray-600 text-white hover:bg-gray-700': props.appearance === 'solid' && props.kind === 'neutral' && !props.disabled,
    'bg-red-600 text-white hover:bg-red-700': props.appearance === 'solid' && props.kind === 'danger' && !props.disabled,
    'border-1 border-emerald-600 text-emerald-600 hover:bg-emerald-50': props.appearance === 'outline' && props.kind === 'brand' && !props.disabled,
    'border-1 border-gray-200 text-gray-600 hover:bg-gray-50': props.appearance === 'outline' && props.kind === 'neutral' && !props.disabled,
    'border-1 border-red-200 text-red-600 hover:bg-red-50': props.appearance === 'outline' && props.kind === 'danger' && !props.disabled,
}));
</script>

<template>
    <button :type="type" :disabled="disabled || loading" :class="baseClasses">
        <div v-if="iconStart">
            <Icon :icon="iconStart" class="w-5 h-5 mr-2"/>
        </div>
        <div v-if="loading" class="mr-2 animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
        <span class="font-[Avenir Next LT Pro] font-sans">{{ loading ? 'Processing, please wait...' : label }}</span>
        <div v-if="iconEnd">
            <Icon :icon="iconEnd" class="w-5 h-5 ml-2"/>
        </div>
    </button>
</template>