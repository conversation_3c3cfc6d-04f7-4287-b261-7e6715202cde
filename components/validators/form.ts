import { ref, reactive } from 'vue';
import type { ValidationErrorMessages, ValidationRules, FieldError, ValidationResult, FormErrors, FormConfig } from '@/types/input';

const defaultErrorMessages: ValidationErrorMessages = {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    password: 'Password does not meet requirements',
    minLength: 'Must be at least {min} characters',
    maxLength: 'Must be no more than {max} characters',
    pattern: 'Invalid format',
    match: 'Fields do not match',
    custom: 'Validation failed',
    array: 'Atleast one item is required'
};

const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const validateRequired = (value: any): boolean => {    
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim() !== '';
    if (typeof value === 'number') return true;
    if (typeof value === 'boolean') return true;
    if (Array.isArray(value)) return value.length > 0;
    return false;
};

const validateEmail = (value: string): boolean => {
    if (!value) return true;
    return emailPattern.test(value);
};

const validatePassword = (
    value: string,
    rules: ValidationRules['password']
): boolean => {
    if (!value || !rules) return true;

    if (rules.minLength && value.length < rules.minLength) return false;
    if (rules.requireUppercase && !/[A-Z]/.test(value)) return false;
    if (rules.requireLowercase && !/[a-z]/.test(value)) return false;
    if (rules.requireNumbers && !/[0-9]/.test(value)) return false;
    if (rules.requireSpecialChars && !/[^a-zA-Z0-9]/.test(value)) return false;

    return true;
};

const validateMinLength = (value: string, min: number): boolean => {
    if (!value) return true;
    return value.length >= min;
};

const validateMaxLength = (value: string, max: number): boolean => {
    if (!value) return true;
    return value.length <= max;
};

const validatePattern = (value: string, pattern: RegExp): boolean => {
    if (!value) return true;
    return pattern.test(value);
};

const validateMatch = (value: string, matchValue: string): boolean => {
    return value === matchValue;
};

const formatErrorMessage = (message: string, params: Record<string, any>): string => {
    return message.replace(/\{(\w+)\}/g, (_, key) => {
        return params[key]?.toString() || '';
    });
};

export const validateField = (
    name: string,
    value: any,
    rules: ValidationRules | undefined,
    formValues: Record<string, any>,
    errorMessages: ValidationErrorMessages = {}
): FieldError | undefined => {
    if (!rules || Object.keys(rules).length === 0) return undefined;

    const messages = { ...defaultErrorMessages, ...errorMessages };

    if (rules.required && !validateRequired(value)) {
        return {
            type: 'required',
            message: messages.required || defaultErrorMessages.required || `${name} field is required`
        };
    }

    if (rules.email && !validateEmail(value as string)) {
        return {
            type: 'email',
            message: messages.email || defaultErrorMessages.email || 'Please enter a valid email address'
        };
    }

    if (rules.password && !validatePassword(value as string, rules.password)) {
        const params: Record<string, any> = {};
        if (rules.password.minLength) params.min = rules.password.minLength;

        return {
            type: 'password',
            message: messages.password || defaultErrorMessages.password || 'Password does not meet requirements',
            params
        };
    }

    if (rules.minLength !== undefined && !validateMinLength(value as string, rules.minLength)) {
        const params = { min: rules.minLength };
        return {
            type: 'minLength',
            message: formatErrorMessage(
                messages.minLength || defaultErrorMessages.minLength || 'Must be at least {min} characters',
                params
            ),
            params
        };
    }

    if (rules.maxLength !== undefined && !validateMaxLength(value as string, rules.maxLength)) {
        const params = { max: rules.maxLength };
        return {
            type: 'maxLength',
            message: formatErrorMessage(
                messages.maxLength || defaultErrorMessages.maxLength || 'Must be no more than {max} characters',
                params
            ),
            params
        };
    }

    if (rules.pattern && !validatePattern(value as string, rules.pattern)) {
        return {
            type: 'pattern',
            message: messages.pattern || defaultErrorMessages.pattern || 'Invalid format'
        };
    }

    if (rules.match && !validateMatch(value as string, formValues[rules.match] as string)) {
        return {
            type: 'match',
            message: messages.match || defaultErrorMessages.match || 'Fields do not match'
        };
    }

    if(rules.array){
        if(value && !Array.isArray(value) && value.length > 0){
            return {
                type: 'array',
                message: messages.array || defaultErrorMessages.array || 'Invalid format'
            };
        }
    }

    if (rules.custom) {
        const result = rules.custom(value, formValues);
        if (result !== true) {
            return {
                type: 'custom',
                message: typeof result === 'string'
                    ? result
                    : (messages.custom || defaultErrorMessages.custom || 'Validation failed')
            };
        }
    }

    return undefined;
};

export const validateForm = (
    values: Record<string, any>,
    config: FormConfig
): ValidationResult => {
    const errors: FormErrors = {};
    let valid = true;

    for (const field of config.fields) {
        const fieldPath = field.name.split('.');
        let fieldValue = values;
        
        for (const key of fieldPath) {
            if (fieldValue === undefined || fieldValue === null) {
                fieldValue = {};
                break;
            }
            fieldValue = fieldValue[key];
        }

        const fieldError = validateField(
            field.name,
            fieldValue,
            field.rules,
            values,
            { ...config.defaultErrorMessages, ...field.errorMessages }
        );

        if (fieldError) {
            errors[field.name] = fieldError;
            valid = false;
        }
    }

    return { valid, errors };
};

export function useFormValidation(config: FormConfig) {
    const formValues = reactive<Record<string, any>>({});
    const formErrors = reactive<FormErrors>({});
    const isValid = ref(true);

    for (const field of config.fields) {
        formValues[field.name] = null;
    }

    const validateFieldValue = (name: string) => {
        const field = config.fields.find(f => f.name === name);
        if (!field) return;

        const error = validateField(
            name,
            formValues[name],
            field.rules,
            formValues,
            { ...config.defaultErrorMessages, ...field.errorMessages }
        );

        if (error) {
            formErrors[name] = error;
        } else {
            delete formErrors[name];
        }

        isValid.value = Object.keys(formErrors).length === 0;
    };

    const validateAllFields = () => {
        const result = validateForm(formValues, config);
        Object.assign(formErrors, result.errors);
        isValid.value = result.valid;
        return result;
    };

    const resetForm = () => {
        for (const field of config.fields) {
            formValues[field.name] = null;
        }
        Object.keys(formErrors).forEach(key => delete formErrors[key]);
        isValid.value = true;
    };

    return {
        formValues,
        formErrors,
        isValid,
        validateField: validateFieldValue,
        validateAll: validateAllFields,
        validateForm,
        resetForm
    };
}