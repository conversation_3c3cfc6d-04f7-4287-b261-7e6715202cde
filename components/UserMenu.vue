<template>
  <div>
    <Menu as="div" class="relative inline-block text-left">
      <div>
        <MenuButton
          class="flex items-center space-x-3 cursor-pointer bg-white hover:bg-gray-50 rounded px-3 py-2 transition-colors duration-200 border border-gray-200 shadow-sm focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
        >
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-emerald-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-semibold">
                {{ getUserInitials(authStore.user?.username) }}
              </span>
            </div>
            <div class="hidden sm:block text-left">
              <div class="text-sm font-medium text-gray-900">{{ authStore.user?.username }}</div>
              <div class="text-xs text-gray-500">Administrator</div>
            </div>
          </div>
          <Icon icon="heroicons:chevron-down-20-solid" class="w-4 h-4 text-gray-400" />
        </MenuButton>
      </div>

      <transition
        enter-active-class="transition duration-100 ease-out"
        enter-from-class="transform scale-95 opacity-0"
        enter-to-class="transform scale-100 opacity-100"
        leave-active-class="transition duration-75 ease-in"
        leave-from-class="transform scale-100 opacity-100"
        leave-to-class="transform scale-95 opacity-0"
      >
        <MenuItems
          class="absolute right-0 mt-0 w-64 origin-top-right bg-white rounded shadow-lg ring-1 ring-gray-200 ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-50"
        >
          <div class="px-4 py-3">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-emerald-600 rounded-full flex items-center justify-center">
                <span class="text-white font-semibold">
                  {{ getUserInitials(authStore.user?.username) }}
                </span>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ authStore.user?.username }}
                </p>
              </div>
            </div>
          </div>


          <div class="py-1">
            <MenuItem v-slot="{ active }">
              <button
                @click="onLogout"
                :class="[
                  active ? 'bg-red-50 text-red-700' : 'text-red-600',
                  'group flex w-full cursor-pointer items-center px-4 py-2 text-sm transition-colors duration-150'
                ]"
              >
                <Icon icon="heroicons:arrow-right-on-rectangle-20-solid" class="mr-3 h-4 w-4" />
                Sign Out
              </button>
            </MenuItem>
          </div>
        </MenuItems>
      </transition>
    </Menu>
  </div>
</template>

<script setup lang="ts">
import { Menu, MenuButton, MenuItems, MenuItem } from "@headlessui/vue";
import { useAuthStore } from "@/store/auth";

const authStore = useAuthStore();
const { start, finish } = useLoadingIndicator();
const router = useRouter();

const getUserInitials = (username?: string): string => {
  if (!username) return "U";
  const parts = username.split(" ");
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase();
  }
  return username.substring(0, 2).toUpperCase();
};

const onLogout = async (): Promise<void> => {
  start();
  try {
    const { success } = await authStore.logout();
    if (success) {
      await navigateTo("/");
    }
  } catch (error) {
    console.error("Logout error:", error);
  } finally {
    finish();
  }
};
</script>
