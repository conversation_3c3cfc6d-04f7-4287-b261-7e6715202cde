<template>
    <button @click="openDialog" class="cursor-pointer">
        <Icon icon="lets-icons:view-alt-duotone" class="w-5 h-5 mr-1" />
    </button>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeDialog" class="relative z-10">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                            <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                                <SectionHeader icon="healthicons:microscope-with-specimen-outline"
                                    :title="`${props.specimen.name}`" description="Viewing details of the specimen" />
                            </DialogTitle>
                            <div class="px-5 py-5">
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">Name</h3>
                                    <p class="text-lg font-semibold text-black">{{ props.specimen.name }}</p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">Preferred name</h3>
                                    <p class="text-lg font-semibold text-black">{{ props.specimen.preferred_name }}</p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">Scientific name</h3>
                                    <p class="text-lg font-semibold text-black">{{ props.specimen.scientific_name }}</p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">IBLIS Mapping name</h3>
                                    <p class="text-lg font-semibold text-black">{{ props.specimen.iblis_mapping_name }}
                                    </p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">Description</h3>
                                    <p class="text-lg font-semibold text-black" v-html="props.specimen.description"></p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">LOINC Code</h3>
                                    <p class="text-lg font-semibold text-black" v-html="props.specimen.loinc_code"></p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">MOH Code</h3>
                                    <p class="text-lg font-semibold text-black" v-html="props.specimen.moh_code"></p>
                                </div>
                                <div class="w-full border-b pb-2 border-gray-100">
                                    <h3 class="text-gray-500 font-base">NLIMS Code</h3>
                                    <p class="text-lg font-semibold text-black" v-html="props.specimen.nlims_code"></p>
                                </div>
                            </div>

                            <div class="px-5 py-2 border-t border-gray-100 flex items-center justify-end">
                                <BaseButton label="Close" @click="closeDialog" />
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue';
import type { Specimen } from '@/types';

const props = defineProps({
    specimen: {
        type: Object as PropType<Specimen>,
        required: true,
    },
});

const isOpen = ref<boolean>(false);

const closeDialog = (): void => {
    isOpen.value = false;
}
const openDialog = (): void => {
    isOpen.value = true;
}
</script>