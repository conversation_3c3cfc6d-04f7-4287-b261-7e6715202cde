<template>
    <div @click="$router.push(props.data.route ?? '#')" class="bg-white border border-gray-100 transition duration-150 cursor-pointer rounded p-6">
        <div class="w-full flex items-center space-x-3">
            <Icon :icon="props.data.icon" class="text-black w-20 h-20" />
            <div class="flex flex-col">
                <p class="text-3xl font-semibold text-black">{{ props.data.count }}</p>
                <p class="text-gray-800 text-xl font-medium">{{ props.data.title }}</p>
                <p class="text-gray-500 text-sm font-light">{{ props.data.description }}</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Card{
    title: string;
    count: number;
    description?: string;
    icon: string;
    route?: string;
}
const props = defineProps({
    data: {
        type: Object as PropType<Card>,
        required: true
    }
})
</script>