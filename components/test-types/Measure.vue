<script setup lang="ts">
import { ref, computed, watch, type PropType } from 'vue';
import BaseButton from '@/components/BaseButton.vue';
import BaseDropdown from '@/components/BaseDropdown.vue';
import { Dialog, DialogPanel, DialogTitle, DialogDescription, DialogOverlay, TransitionRoot } from '@headlessui/vue';
import type { TestCatalog, MeasureType, Measure } from '@/types';
import type { FieldError } from '@/types/input';

const props = defineProps({
  initialTestCatalog: {
    required: true,
    type: Object as PropType<TestCatalog>
  },
  testCatalog: {
    required: true,
    type: Object as PropType<TestCatalog>
  },
  errors: {
    required: true,
    type: Object as PropType<Record<string, FieldError>>
  }
})

const emit = defineEmits<{
  (e: 'update:testCatalog', value: TestCatalog): void;
  (e: 'measureAdded'): void;
  (e: 'measureRemoved', index: number): void;
  (e: 'rangeAdded', measureIndex: number): void;
  (e: 'rangeRemoved', measureIndex: number, rangeIndex: number): void;
}>();

const { $metadata }: any = useNuxtApp();
const confirmRemoveRange = ref(false);
const measureTypes = ref<MeasureType[]>([]);
const pendingMeasureTypeChange = ref<{ value: MeasureType | null, index: number }>({ value: null, index: -1 });
const selectedMeasureType = ref<MeasureType | null>(null);

watch(() => props.initialTestCatalog, (newVal) => {
  if (newVal) {
    Object.assign(props.testCatalog, newVal);
  }
}, { deep: true });

watch(() => props.testCatalog, (newVal) => {
  emit('update:testCatalog', newVal);
}, { deep: true });

const getMeasureType = (typeId: number): MeasureType | undefined => {
  return measureTypes.value.find((type: any) => type.id === typeId);
};

const selectedMeasure = computed(() => {
  return {
    name: 'Reference',
    description: 'Clinical reference values'
  };
});

const createEmptyMeasure = (): Measure => {
  return {
    name: '',
    short_name: '',
    unit: '',
    measure_type_id: 6,
    description: '',
    loinc_code: '',
    moh_code: '',
    nlims_code: '',
    preferred_name: '',
    scientific_name: '',
    measure_ranges_attributes: []
  };
}

const addNewMeasure = () => {
  props.testCatalog.measures.push(createEmptyMeasure());
};


const removeMeasure = (index: number): void => {
  props.testCatalog.measures.splice(index, 1);
  emit('measureRemoved', index);
}

const setSelectedMeasureType = (val: MeasureType, index: number) => {
  const currentMeasure = props.testCatalog.measures[index];

  if (currentMeasure.measure_ranges_attributes.length > 0) {
    pendingMeasureTypeChange.value = { value: val, index };
    confirmRemoveRange.value = true;
  } else {
    currentMeasure.measure_type_id = val.id;
    selectedMeasureType.value = val;
  }
};

const confirmChange = () => {
  if (pendingMeasureTypeChange.value.value && pendingMeasureTypeChange.value.index >= 0) {
    const index = pendingMeasureTypeChange.value.index;
    const measureType = pendingMeasureTypeChange.value.value;

    props.testCatalog.measures[index].measure_ranges_attributes = [];

    props.testCatalog.measures[index].measure_type_id = measureType.id;
    selectedMeasureType.value = measureType;

    confirmRemoveRange.value = false;
  }
};

const revertChange = () => {
  confirmRemoveRange.value = false;
  pendingMeasureTypeChange.value = { value: null, index: -1 };
};


const createNewRange = (type: string): any => ({
  id: Date.now() + Math.random(),
  ...(type === 'ranges'
    ? { age_min: null, age_max: null, range_lower: null, range_upper: null, sex: null }
    : { value: null, interpretation: null })
});

const addNewRange = (measureIndex: number): void => {
  const measure = props.testCatalog.measures[measureIndex];
  const measureType = getMeasureType(measure.measure_type_id);
  const type = measureType?.structure?.type || "ranges";
  measure.measure_ranges_attributes = Array.isArray(measure.measure_ranges_attributes)
    ? measure.measure_ranges_attributes
    : [];
  const newRange = createNewRange(type);
  measure.measure_ranges_attributes.push(newRange);
  emit('rangeAdded', measureIndex);
};

const removeRange = (measureIndex: number, rangeIndex: number): void => {
  props.testCatalog.measures[measureIndex].measure_ranges_attributes.splice(rangeIndex, 1);
  emit('rangeRemoved', measureIndex, rangeIndex);
}

onMounted(async () => {
  try {
    const measureTypesData = await $metadata.measure_types.getAll();
    measureTypes.value = measureTypesData

  } catch (error) {
    console.error('Failed to load resourcesbbb:', error)
  }
})
</script>

<template>
  <div>
    <div class="mb-8">
      <div class="w-full flex justify-between items-center mb-4">
        <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3 w-full">
          <h2 class="text-xl font-semibold">Measures</h2>
        </div>
      </div>

      <div class="flex justify-end mb-4">
        <BaseButton label="Add Measure" icon-start="mdi:plus" @click="addNewMeasure" />
      </div>

      <div v-for="(measure, index) in testCatalog.measures" :key="index"
        class="mb-8 p-4 bg-gray-50 rounded border border-gray-200">
        <div class="flex justify-between mb-4">
          <h3 class="font-medium text-lg">Measure ({{ index + 1 }})</h3>
          <button type="button" @click="removeMeasure(index)"
            class="text-red-500 hover:font-medium flex items-center hover:text-red-600 cursor-pointer">
            <svg class="mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
              <g fill="none">
                <circle cx="12" cy="12" r="9" fill="currentColor" fill-opacity="0.25" />
                <path stroke="currentColor" stroke-width="1.2" d="M8 12h8" />
              </g>
            </svg>Remove
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <BaseInput label="Name" name="measure_name" v-model="measure.name" :id="`measure-name-${index}`" v-bind:error-message="errors.name?.message" required/>
          </div>

          <div>
            <BaseInput label="Short name" name="measure_short_name" v-model="measure.short_name" :id="`measure-short-name-${index}`" v-bind:error-message="errors.short_name?.message"/>
          </div>

          <div>
            <BaseInput label="Preferred name" name="measure_preferred_name" v-model="measure.preferred_name" :id="`measure-preferred-name-${index}`" v-bind:error-message="errors.preferred_name?.message" required/>
          </div>

          <div>
            <BaseInput label="Scientific name" name="measure_scientific_name" v-model="measure.scientific_name" :id="`measure-scientific-name-${index}`" v-bind:error-message="errors.scientific_name?.message" required/>
          </div>

          <div>
            <BaseInput label="Unit" name="measure_unit" v-model="measure.unit" :id="`measure-unit-${index}`" v-bind:error-message="errors.unit?.message"/>
          </div>

          <BaseDropdown name="measure-type" label="Type" :hideNotSelected="true"
            :options="measureTypes.map((type: any) => { return { ...type, label: type.name } })"
            v-model="measure.measure_type_id" @change="(val) => setSelectedMeasureType(val, index)" />

          <div class="md:col-span-2 relative z-0">
            <label for="description" class="block font-medium text-gray-700 mb-1">Description</label>
            <div class="overflow-hidden">
              <RichTextEditor theme="snow" class="editor" v-model:content="measure.description" contentType="html">
              </RichTextEditor>
            </div>
          </div>

          <div>
            <label class="block text-base font-medium text-gray-700 mb-2">LOINC Code</label>
            <input :id="`measure-loinc-code-${index}`" name="measure_loinc_code" v-model="measure.loinc_code"
              type="text" required
              class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
          </div>

          <div>
            <label class="block text-base font-medium text-gray-700 mb-2">MOH Code</label>
            <input :id="`measure-moh-code-${index}`" name="measure_moh_code" v-model="measure.moh_code" type="text"
              required
              class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
          </div>
        </div>

        <div class="mt-4">
          <div class="flex justify-between items-center mb-3">
            <h4 class="font-medium text-lg text-gray-700">Reference Ranges</h4>
            <button type="button" @click="addNewRange(index)"
              class="px-3 py-1 cursor-pointer bg-emerald-100 text-emerald-700 rounded hover:bg-emerald-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 text-sm">
              Add Range
            </button>
          </div>
          <div class="bg-white rounded border border-gray-200 mb-3 px-5 py-4"
            v-for="(range, rangeIndex) in measure.measure_ranges_attributes" v-bind:key="range.id">
            <div class="w-full flex items-center justify-between space-x-2">
              <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 24 24">
                  <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M4 6h16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2m1 2.5h1.5m1.5 0H6.5m0 0v7m0 0H5m1.5 0H8" />
                </svg>
                <div>
                  <h3 class="text-lg font-medium text-black">
                    {{ selectedMeasure.name }} Range ({{ rangeIndex + 1 }})
                    <p class="font-thin text-sm -mt-1">{{ selectedMeasure.description }}</p>
                  </h3>
                </div>
              </div>
              <button type="button" @click="removeRange(index, rangeIndex)"
                class="text-red-600 cursor-pointer hover:text-red-800 flex items-center">
                <svg class="mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                  <g fill="none">
                    <circle cx="12" cy="12" r="9" fill="currentColor" fill-opacity="0.25" />
                    <path stroke="currentColor" stroke-width="1.2" d="M8 12h8" />
                  </g>
                </svg>
                Remove
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3"
              v-if="getMeasureType(measure.measure_type_id)?.structure?.type?.toLowerCase() === 'ranges'">
              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Age Min</label>
                <input type="number" name="min_age" v-if="'age_min' in range" v-model="range.age_min"
                  :id="String(range?.id)"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
              </div>

              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Age Max</label>
                <input type="number" name="max_age" v-if="'age_max' in range" v-model="range.age_max"
                  :id="String(range?.id)"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
              </div>

              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Range Lower</label>
                <input type="number" name="range_lower" v-if="'range_lower' in range" v-model="range.range_lower"
                  :id="String(range?.id)"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
              </div>

              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Range Upper</label>
                <input type="number" name="range_upper" v-if="'range_upper' in range" v-model="range.range_upper"
                  :id="String(range?.id)"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
              </div>

              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Sex</label>
                <select name="sex" v-if="'sex' in range" v-model="range.sex"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200">
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Both">Both</option>
                </select>
              </div>
            </div>

            <div class="w-full grid grid-cols-2 gap-5"
              v-else-if="getMeasureType(measure.measure_type_id)?.structure?.type === 'options'">
              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Value</label>
                <input name="value" :key="String(range.id)" v-model="range.value"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
              </div>
              <div>
                <label class="block text-base font-medium text-gray-700 mb-2">Interpretation</label>
                <input name="interpretation" v-model="range.interpretation" :key="String(range.id)"
                  class="w-full px-3 py-1 border border-gray-200 text-base placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500 text-gray-900 transition duration-200" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="testCatalog.measures.length == 0" class="flex flex-col items-center justify-center">
        <svg class="w-auto h-24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
          <g fill="none">
            <path fill="currentColor" fill-opacity="0.16"
              d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2" />
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"
              stroke-width="1.5"
              d="M12 16h.008M12 8v5m10-1c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10" />
          </g>
        </svg>
        <p class="text-base"> Please add atleast one measure to continue</p>
      </div>
    </div>
  </div>

  <TransitionRoot :show="confirmRemoveRange" as="template">
    <Dialog as="div" class="fixed inset-0 z-10 overflow-y-auto">
      <div class="min-h-screen px-4 text-center">
        <DialogOverlay class="fixed inset-0 bg-black opacity-30" />
        <span class="inline-block h-screen align-middle" aria-hidden="true">​</span>
        <DialogPanel
          class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <DialogTitle class="text-lg font-medium leading-6 text-gray-900">
            Changing measure type will reset existing ranges.
          </DialogTitle>
          <DialogDescription class="mt-2 text-lg text-gray-500">
            Do you want to continue?
          </DialogDescription>
          <div class="mt-4 flex justify-end space-x-4">
            <button @click="confirmChange"
              class="bg-emerald-600 cursor-pointer text-white px-4 py-2 rounded">Yes</button>
            <button @click="revertChange" class="bg-gray-200 cursor-pointer text-gray-900 px-4 py-2 rounded">No</button>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  </TransitionRoot>
</template>