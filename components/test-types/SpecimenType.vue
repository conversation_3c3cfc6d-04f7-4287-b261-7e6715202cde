<template>
    <div class="mb-8">
        <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3">
            <h2 class="text-xl font-semibold">Specimen Type(s)</h2>
        </div>
        
        <BaseSearchbar v-model="search" clearable placeholder="Search specimens..." />

        <div v-if="errorMessage && Object.keys(errorMessage).length > 0" class="mb-3 text-red-600 text-sm">
            {{ errorMessage }}
        </div>

        <div class="w-full grid grid-cols-5 gap-2 mt-5">
            <div v-for="specimen in filteredSpecimens" :key="specimen.id" class="flex items-center">
                <input 
                    :id="`specimen-${specimen.id}`"
                    type="checkbox"
                    :value="specimen.id"
                    v-model="selectedSpecimens"
                    class="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                    @change="updateSpecimens"
                />
                <label :for="`specimen-${specimen.id}`" class="ml-2 block text-gray-700">
                    {{ specimen.name }}
                </label>
            </div>

            <div v-if="filteredSpecimens.length === 0 && search" class="w-full col-span-5 flex flex-col space-y-3 items-center justify-center py-10">
                <svg class="w-auto h-20 text-black" xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
                    <path fill="currentColor" d="m345.1 41.52l-4.3 16.1l88.8 23.78l4.3-16.1zM350.2 86l-42 157c27.3 6.5 44.1 17.1 52.4 29.2l45.8-171.1l-20-5.4l-31.7 118.4l-16.1-4.3l31.7-118.4l15.8 4.2zm-162.6 35.5c-6.2.2-12.3 1.4-18.5 3.8c-34.7 13.6-121.43 91.9-121.43 91.9l-1.1 171.9c49.49-9.8 98.73-11.5 119.53-67.9l38.8-24.2s75.3 40.5 118.6 8.7c23.8-17.3 2.8-39-64-52.7c-114.2-23.5-93.3-50.3-93.3-50.3l27.1 8.6l-18.6-35l31.6 29.7l7.3-29.7l13.3 17.7l66 24.9l15.2-55.3c-53.7-14.9-87.4-43-120.5-42.1m214.3 81.6l-14.8 55.4c31.5 16.8 71.8-30.1 14.8-55.4m-17.6 74.8l-14.1 57.2c38.3 13.5 82.4-34.3 14.1-57.2m-39.2 52.2c-15.8 12.3-39 20.6-65.7 20.3L254.9 442c-4.1 15 5.1 31 20.6 35s31.5-5 35.6-20z" />
                </svg>
                <p class="text-black text-xl">No specimen types found</p>
                <NuxtLink class="text-sky-500 hover:text-sky-600 transition duration-150">Refresh</NuxtLink>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref, onMounted, type PropType } from 'vue';
import type { Specimen, TestCatalog } from '@/types';

const props = defineProps({
    errorMessage: {
        type: String,
        default: '',
    },
    data: {
        type: Object as PropType<TestCatalog>,
        required: true,
    },
});

const emit = defineEmits(['update:selectedSpecimens']);
const search = ref<string>('');
const selectedSpecimens = ref<number[]>([]);
const specimenTypes = ref<Specimen[]>([]);
const { $metadata }: any = useNuxtApp();

const filteredSpecimens = computed(() => {
    if (!specimenTypes.value) return [];
    return specimenTypes.value.filter((specimen) =>
        specimen.name.toLowerCase().includes(search.value.toLowerCase())
    );
});

const updateSpecimens = () => {
    emit('update:selectedSpecimens', selectedSpecimens.value);
};

onMounted(async () => {
    try {
        const specimenTypesData = await $metadata.specimen_types.getAll();
        specimenTypes.value = specimenTypesData;
        if(props.data){
            selectedSpecimens.value = props.data.specimen_types || [];
        }
    } catch (error) {
        console.error('Failed to load specimens:', error);
    }
});
</script>
