<template>
  <div>
    <div class="mb-8">
      <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3 flex justify-between items-center">
        <h2 class="text-xl font-semibold">Organisms</h2>
        <button
          v-if="hasError"
          @click="loadOrganisms"
          class="text-emerald-600 hover:text-emerald-700 flex items-center gap-1"
        >
          <Icon icon="mdi:refresh" class="w-5 h-5" />
          Retry
        </button>
      </div>

      <BaseSearchbar
        v-model="search"
        clearable
        placeholder="Search organisms..."
      />

      <div v-if="isLoading" class="w-full flex justify-center items-center py-10">
        <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="oklch(0.596 0.145 163.225)"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="M36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
      </div>

      <div v-else-if="hasError" class="w-full flex flex-col items-center justify-center py-10">
        <Icon icon="mdi:alert-circle" class="w-auto h-16 text-red-500 mb-2" />
        <p class="text-gray-700 mb-2">Failed to load organisms</p>
        <button
          @click="loadOrganisms"
          class="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition"
        >
          Refresh
        </button>
      </div>

      <div v-else>
        <div class="w-full grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 mt-5 max-h-80 overflow-y-auto">
          <div
            v-for="organism in paginatedOrganisms"
            v-bind:key="organism.id"
            class="flex items-center mb-2"
          >
            <div class="flex items-center">
              <input
                :id="`organism-${organism.id}`"
                type="checkbox"
                :value="organism.id"
                v-model="selectedOrganisms"
                class="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500 cursor-pointer"
              />
              <label
                :for="`organism-${organism.id}`"
                class="ml-2 block cursor-pointer text-gray-700 select-none"
              >
                {{ organism.name }}
              </label>
            </div>
          </div>

          <div
            v-if="filteredOrganisms.length === 0 && !isLoading"
            class="w-full col-span-5 flex flex-col space-y-3 items-center justify-center py-10"
          >
            <Icon
              icon="covid:covid19-virus-lifelong-2"
              class="w-auto h-20 text-black"
            />
            <p class="text-black text-xl">No organisms found</p>
          </div>
        </div>

        <div v-if="totalPages > 1" class="flex justify-center items-center mt-4 space-x-2">
          <button
            @click="prevPage"
            type="button"
            :disabled="currentPage === 1"
            :class="[
              'px-3 py-1 rounded cursor-pointer',
              currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-emerald-100 text-emerald-700 hover:bg-emerald-200'
            ]"
          >
            Previous
          </button>
          <span class="text-sm text-gray-600">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="nextPage"
            type="button"
            :disabled="currentPage === totalPages"
            :class="[
              'px-3 py-1 rounded cursor-pointer',
              currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-emerald-100 text-emerald-700 hover:bg-emerald-200'
            ]"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, defineProps, defineEmits, type PropType } from "vue";
import type { Organism, TestCatalog } from "@/types";

const props = defineProps({
  testCatalog: {
    type: Object as PropType<TestCatalog>,
    required: true,
  },
});

const { $metadata }: any = useNuxtApp();
const emit = defineEmits(["update:selectedOrganisms"]);

const organisms = ref<Organism[]>([]);
const search = ref<string>("");
const selectedOrganisms = ref<number[]>([...props.testCatalog.organisms]);
const isLoading = ref(true);
const hasError = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(40);

watch(search, () => {
  currentPage.value = 1;
});

const filteredOrganisms = computed(() => {
  if (!organisms.value.length) return [];

  const searchTerm = search.value.toLowerCase();
  if (!searchTerm) return organisms.value;

  return organisms.value.filter(organism =>
    organism.name.toLowerCase().includes(searchTerm)
  );
});

const totalPages = computed(() =>
  Math.ceil(filteredOrganisms.value.length / itemsPerPage.value) || 1
);

const paginatedOrganisms = computed(() => {
  const startIdx = (currentPage.value - 1) * itemsPerPage.value;
  const endIdx = startIdx + itemsPerPage.value;
  return filteredOrganisms.value.slice(startIdx, endIdx);
});

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    nextTick(() => {
      document.querySelector('.max-h-80')?.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    nextTick(() => {
      document.querySelector('.max-h-80')?.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
};

const emitSelection = () => {
  emit("update:selectedOrganisms", selectedOrganisms.value);
};

const loadOrganisms = async () => {
  isLoading.value = true;
  hasError.value = false;

  try {
    const data = await $metadata.organisms.getAll();
    organisms.value = data;
  } catch (error) {
    hasError.value = true;
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  loadOrganisms();
});

watch(selectedOrganisms, emitSelection, { deep: true });
</script>
