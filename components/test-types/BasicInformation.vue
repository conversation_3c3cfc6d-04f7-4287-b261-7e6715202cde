<script setup lang="ts">
import { defineProps, defineEmits, type PropType } from "vue";
import BaseInput from "@/components/BaseInput.vue";
import BaseDropdown from "@/components/BaseDropdown.vue";
import type { Department, TestCatalog } from "@/types";
import type { Option } from "@/types/form";

interface Errors {
  test_type_name?: { type: string; message: string };
  short_name?: { type: string; message: string };
  preferred_name?: { type: string; message: string };
  scientific_name?: { type: string; message: string };
  iblis_mapping_name?: { type: string; message: string };
  loinc_code?: { type: string; message: string };
  moh_code?: { type: string; message: string };
  target_tat?: { type: string; message: string };
  can_be_done_on_sex?: { type: string; message: string };
  test_category_id?: { type: string; message: string };
}

defineProps({
  testCatalog: {
    type: Object as PropType<TestCatalog>,
    required: true,
  },
  errors: {
    type: Object as PropType<Errors>,
    required: true,
  },
});

const { $metadata }: any = useNuxtApp();
const sexOptions: Option[] = [
  { id: "Male", label: "Male" },
  { id: "Female", label: "Female" },
  { id: "Both", label: "Both" },
];
const departments = ref<Department[]>([]);
const emit = defineEmits(["update:testCatalog"]);

onMounted(async () => {
  try {
    const data = await $metadata.departments.getAll();
    if (data) {
      departments.value = data;
    }
  } catch (error) {
    console.error("Failed to load specimens:", error);
  }
});

const filteredDepartments = computed(() => {
  if (departments.value.length === 0) return [];
  const uniqueDepartments = new Map();
  departments.value.forEach((department: Department) => {
    uniqueDepartments.set(department.id, {
      id: department.id,
      label: department.name,
    });
  });

  return Array.from(uniqueDepartments.values());
});
</script>

<style scoped>
.editor {
  min-height: 30px;
  overflow-y: auto;
}
</style>

<template>
  <div class="mb-8 relative">
    <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3">
      <h2 class="text-xl font-semibold">Basic Information</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <BaseInput
        label="Name"
        name="test_type_name"
        v-model="testCatalog.test_type.name"
        v-bind:error-message="errors?.test_type_name?.message"
        required
      />

      <BaseInput
        label="Short name"
        name="short_name"
        v-model="testCatalog.test_type.short_name"
        v-bind:error-message="errors.short_name?.message"
      />

      <BaseInput
        label="Preferred name"
        name="preferred_name"
        v-model="testCatalog.test_type.preferred_name"
        v-bind:error-message="errors.preferred_name?.message"
        required
      />

      <BaseInput
        label="Scientific name"
        name="scientific_name"
        v-model="testCatalog.test_type.scientific_name"
        v-bind:error-message="errors.scientific_name?.message"
      />

      <BaseInput
        label="IBLIS mapping name"
        name="iblis_mapping_name"
        v-model="testCatalog.test_type.iblis_mapping_name"
        v-bind:error-message="errors.iblis_mapping_name?.message"
      />

      <div class="md:col-span-2 relative z-0">
        <label for="description" class="block font-medium text-gray-700 mb-1"
          >Description</label
        >
        <div>
          <div class="overflow-hidden">
            <RichTextEditor
              theme="snow"
              class="editor"
              v-model:content="testCatalog.test_type.description"
              contentType="html"
            >
            </RichTextEditor>
          </div>
        </div>
      </div>

      <BaseInput
        label="LOINC Code"
        name="lonic_code"
        v-model="testCatalog.test_type.loinc_code"
        v-bind:error-message="errors.loinc_code?.message"
        icon-start="bi:qr-code"
      />

      <BaseInput
        label="MOH Code"
        name="moh_code"
        v-model="testCatalog.test_type.moh_code"
        v-bind:error-message="errors.moh_code?.message"
        icon-start="bi:qr-code"
      />

      <BaseInput
        label="Target TAT"
        name="target_tat"
        v-model="testCatalog.test_type.targetTAT"
        v-bind:error-message="errors.target_tat?.message"
      />

      <BaseDropdown
        v-model="testCatalog.test_type.can_be_done_on_sex"
        :options="sexOptions"
        label="Can Be Done On Sex"
        name="can_be_done_on_sex"
        v-bind:error-message="errors.can_be_done_on_sex?.message"
        required
      />

      <BaseDropdown
        v-model="testCatalog.test_type.test_category_id"
        :options="filteredDepartments"
        label="Test Category"
        name="test_category_id"
        required
        v-bind:error-message="errors.test_category_id?.message"
      />
    </div>
  </div>
</template>
