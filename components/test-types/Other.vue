<template>
    <div class="mb-8">
        <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3">
            <h2 class="text-xl font-semibold">Other information</h2>
        </div>

        <div class="w-full grid grid-cols-2 gap-5">
            <BaseInput name="assay_format" label="Assay format" v-model="props.testCatalog.test_type.assay_format"/>
            <BaseInput name="hr_cadre_required" label="Human Resource Cadre" icon-start="guidance:office-pod" v-model="props.testCatalog.test_type.hr_cadre_required" />
            <BaseDropdown name="equipment" label="Equipment required" is-multiple v-model="props.testCatalog.equipment" :options="filteredEquipmentOptions" :loading="metadataLoading" />
            <BaseDropdown name="lab_test_sites" label="Laboratory Levels" is-multiple v-model="props.testCatalog.lab_test_sites" :options="filteredLaboratoryOptions" :loading="metadataLoading" />
        </div>
    </div>
</template>

<script setup lang="ts">
import type { LabTestSite, Equipment, TestCatalog } from '@/types';
import type { Option } from '@/types/form';

const props = defineProps({
    currentStep: {
        type: Number,
        required: true,
    },
    testCatalog: {
        type: Object as PropType<TestCatalog>,
        required: true,
    }
});

const { $metadata }: any = useNuxtApp();
const laboratorySites = ref<LabTestSite[]>([]);
const equipments = ref<Equipment[]>([]);
const metadataLoading = ref<boolean>(false);

const filteredLaboratoryOptions = computed((): Option[] => {
    return laboratorySites.value.length > 0 ? laboratorySites.value.map((level: LabTestSite) => ({
        label: level.name,
        id: level.id,
    })) : [];
});

const filteredEquipmentOptions = computed((): Option[] => {
    return equipments.value.length > 0 ? equipments.value.map((equipment: Equipment) => ({
        label: equipment.name,
        id: equipment.id,
    })) : [];
});

onMounted(async () => {
    try {
        metadataLoading.value = true;
        const [laboratorySitesData, equipmentsData] = await Promise.all([
            $metadata.lab_test_sites.getAll(),
            $metadata.equipments.getAll()
        ]);

        laboratorySites.value = laboratorySitesData;
        equipments.value = equipmentsData;
    } catch (error) {
        console.error('Failed to load resources:', error);
    } finally {
        metadataLoading.value = false;
    }
});
</script>

<style scoped></style>
