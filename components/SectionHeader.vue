<template>
    <div :class="[
        'flex items-center space-x-5 px-4 py-2',
        mode === 'primary' ? 'bg-emerald-600 text-emerald-100' : 
        mode === 'warning' ? 'bg-amber-600 text-amber-100' :
        mode === 'error' ? 'bg-red-600 text-red-100' :
        mode === 'info' ? 'bg-blue-600 text-blue-100' :
        'bg-emerald-600 text-emerald-100'
    ]">
        <Icon v-if="icon" :icon="icon" class="w-auto h-28" />
        <div class="flex flex-col">
            <h1 class="text-2xl font-bold">{{ title }}</h1>
            <p :class="[
                mode === 'primary' ? 'text-emerald-100' : 
                mode === 'warning' ? 'text-amber-100' :
                mode === 'error' ? 'text-red-100' :
                mode === 'info' ? 'text-blue-100' :
                'text-emerald-100'
            ]">{{ description }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    mode: {
        type: String as PropType<'primary' | 'warning' | 'error' | 'info'>,
        default: 'primary'
    },
    icon: {
        type: String,
        required: false
    },
    title: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    }
});
</script>