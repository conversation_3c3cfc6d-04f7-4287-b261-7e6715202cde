<template>
  <div>
    <slot name="trigger">
      <button
        @click="openModal"
        class="cursor-pointer bg-white border border-gray-200 transition duration-150 flex items-center px-2.5 py-2 rounded"
      >
      <svg class="w-5 h-5 mr-1" xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256"><g fill="currentColor"><path d="M216 48v160H40V48a16 16 0 0 1 16-16h144a16 16 0 0 1 16 16" opacity="0.2"/><path d="M224 144v64a8 8 0 0 1-8 8H40a8 8 0 0 1-8-8v-64a8 8 0 0 1 16 0v56h160v-56a8 8 0 0 1 16 0M93.66 77.66L120 51.31V144a8 8 0 0 0 16 0V51.31l26.34 26.35a8 8 0 0 0 11.32-11.32l-40-40a8 8 0 0 0-11.32 0l-40 40a8 8 0 0 0 11.32 11.32"/></g></svg>
        Import
      </button>
    </slot>

    <TransitionRoot appear :show="isOpen" as="template">
      <Dialog as="div" @close="closeModal" class="relative z-[9999]">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black/25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-lg transform overflow-hidden rounded bg-white p-6 text-left align-middle shadow-xl transition-all"
              >
                <DialogTitle
                  as="h3"
                  class="text-lg font-medium leading-6 text-gray-900"
                >
                  {{ title || "Import Data" }}
                </DialogTitle>

                <div class="mt-4">
                  <p class="text-sm text-gray-500 mb-4">
                    {{
                      description ||
                      "Upload an Excel file to import data. The system will process and import valid entries."
                    }}
                  </p>

                  <div class="mt-2">
                    <input
                      type="file"
                      ref="fileInput"
                      @change="handleFileChange"
                      :accept="props.acceptedFileTypes"
                      class="hidden"
                    />

                    <div
                      @click="triggerFileInput"
                      class="border-2 border-dashed border-gray-300 rounded py-8 px-4 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50"
                      :class="{ 'pointer-events-none': isUploading }"
                    >
                      <svg
                        class="w-auto h-20 mb-2"
                        xmlns="http://www.w3.org/2000/svg"
                        width="32"
                        height="32"
                        viewBox="0 0 32 32"
                      >
                        <defs>
                          <linearGradient
                            id="vscodeIconsFileTypeExcel0"
                            x1="4.494"
                            x2="13.832"
                            y1="-2092.086"
                            y2="-2075.914"
                            gradientTransform="translate(0 2100)"
                            gradientUnits="userSpaceOnUse"
                          >
                            <stop offset="0" stop-color="#18884f" />
                            <stop offset=".5" stop-color="#117e43" />
                            <stop offset="1" stop-color="#0b6631" />
                          </linearGradient>
                        </defs>
                        <path
                          fill="#185c37"
                          d="M19.581 15.35L8.512 13.4v14.409A1.19 1.19 0 0 0 9.705 29h19.1A1.19 1.19 0 0 0 30 27.809V22.5Z"
                        />
                        <path
                          fill="#21a366"
                          d="M19.581 3H9.705a1.19 1.19 0 0 0-1.193 1.191V9.5L19.581 16l5.861 1.95L30 16V9.5Z"
                        />
                        <path fill="#107c41" d="M8.512 9.5h11.069V16H8.512Z" />
                        <path
                          d="M16.434 8.2H8.512v16.25h7.922a1.2 1.2 0 0 0 1.194-1.191V9.391A1.2 1.2 0 0 0 16.434 8.2"
                          opacity="0.1"
                        />
                        <path
                          d="M15.783 8.85H8.512V25.1h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191"
                          opacity="0.2"
                        />
                        <path
                          d="M15.783 8.85H8.512V23.8h7.271a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191"
                          opacity="0.2"
                        />
                        <path
                          d="M15.132 8.85h-6.62V23.8h6.62a1.2 1.2 0 0 0 1.194-1.191V10.041a1.2 1.2 0 0 0-1.194-1.191"
                          opacity="0.2"
                        />
                        <path
                          fill="url(#vscodeIconsFileTypeExcel0)"
                          d="M3.194 8.85h11.938a1.193 1.193 0 0 1 1.194 1.191v11.918a1.193 1.193 0 0 1-1.194 1.191H3.194A1.19 1.19 0 0 1 2 21.959V10.041A1.19 1.19 0 0 1 3.194 8.85"
                        />
                        <path
                          fill="#fff"
                          d="m5.7 19.873l2.511-3.884l-2.3-3.862h1.847L9.013 14.6c.116.234.2.408.238.524h.017q.123-.281.26-.546l1.342-2.447h1.7l-2.359 3.84l2.419 3.905h-1.809l-1.45-2.711A2.4 2.4 0 0 1 9.2 16.8h-.024a1.7 1.7 0 0 1-.168.351l-1.493 2.722Z"
                        />
                        <path
                          fill="#33c481"
                          d="M28.806 3h-9.225v6.5H30V4.191A1.19 1.19 0 0 0 28.806 3"
                        />
                        <path fill="#107c41" d="M19.581 16H30v6.5H19.581Z" />
                      </svg>
                      <p v-if="!selectedFile" class="text-sm text-gray-500">
                        Click to select a file (Excel or CSV)
                      </p>
                      <p v-else class="text-sm text-emerald-600 font-medium">
                        {{ selectedFile.name }}
                      </p>
                    </div>

                    <div v-if="fileError" class="mt-2 text-red-500 text-sm">
                      {{ fileError }}
                    </div>

                    <div v-if="isUploading" class="mt-4">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium text-emerald-700"
                          >Uploading...</span
                        >
                        <span class="text-sm font-medium text-emerald-700"
                          >{{ uploadProgress }}%</span
                        >
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          class="bg-emerald-600 h-2.5 rounded-full transition-all duration-300"
                          :style="{ width: `${uploadProgress}%` }"
                        ></div>
                      </div>
                      <p class="text-xs text-gray-500 mt-1">
                        {{ statusMessage }}
                      </p>
                    </div>

                    <div
                      v-if="importResults"
                      class="mt-4 p-3 bg-gray-50 rounded border border-gray-100"
                    >
                      <h4 class="font-medium text-gray-800 mb-3">
                        Import Summary
                      </h4>

                      <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                          <span>Total Records:</span>
                          <span class="font-medium">{{
                            importResults.totalRecords
                          }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                          <span>Successfully Imported:</span>
                          <span class="font-medium text-emerald-600">{{
                            importResults.successful
                          }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                          <span>Failed Records:</span>
                          <span class="font-medium text-red-600">{{
                            importResults.failed
                          }}</span>
                        </div>
                      </div>

                      <div v-if="importResults.details" class="pt-3">
                        <h5 class="text-sm font-medium text-gray-700 mb-2">
                          Breakdown by Type:
                        </h5>
                        <div
                          v-for="(detail, type) in importResults.details"
                          :key="type"
                          class="mb-3"
                        >
                          <div
                            class="text-sm font-medium text-gray-600 capitalize mb-1"
                          >
                            {{ String(type).replace("_", " ") }}:
                          </div>
                          <div class="pl-0 space-y-1 text-xs">
                            <div class="flex justify-between">
                              <span>Total:</span>
                              <span class="font-medium">{{
                                detail.total
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span>Created:</span>
                              <span class="font-medium text-emerald-600">{{
                                detail.created
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span>Skipped:</span>
                              <span class="font-medium text-yellow-600">{{
                                detail.skipped
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span>Failed:</span>
                              <span class="font-medium text-red-600">{{
                                detail.failed
                              }}</span>
                            </div>

                            <div
                              v-if="detail.errors && detail.errors.length > 0"
                              class="mt-2"
                            >
                              <button
                                @click="toggleErrors(String(type))"
                                class="text-xs text-red-600 flex items-center cursor-pointer hover:text-red-800 underline"
                              >
                                <svg
                                  class="w-4 h-4 mr-2"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fill="currentColor"
                                    d="M10 2a8 8 0 1 1 0 16a8 8 0 0 1 0-16m0 1a7 7 0 1 0 0 14a7 7 0 0 0 0-14m0 9.5a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5M10 6a.5.5 0 0 1 .492.41l.008.09V11a.5.5 0 0 1-.992.09L9.5 11V6.5A.5.5 0 0 1 10 6"
                                  />
                                </svg>

                                {{
                                  expandedErrors[String(type)] ? "Hide" : "Show"
                                }}
                                {{ detail.errors.length }} error(s)
                              </button>

                              <div
                                v-if="expandedErrors[String(type)]"
                                class="mt-1 max-h-32 overflow-y-auto"
                              >
                                <div
                                  v-for="(error, index) in detail.errors"
                                  :key="index"
                                  class="text-xs text-red-600 bg-red-50 p-2 rounded mb-1 border border-red-100"
                                >
                                  <div class="font-medium">
                                    Row {{ error.row }}:
                                  </div>
                                  <div class="text-red-700">
                                    {{ error.error }}
                                  </div>
                                  <div
                                    v-if="error.test_type"
                                    class="text-gray-600 mt-1"
                                  >
                                    Test Type: {{ error.test_type }}
                                  </div>
                                  <div
                                    v-if="error.measure_name"
                                    class="text-gray-600"
                                  >
                                    Measure: {{ error.measure_name }}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-6 flex justify-between">
                  <button
                    type="button"
                    class="inline-flex justify-center rounded border border-transparent bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300 focus:outline-none"
                    @click="closeModal"
                  >
                    Cancel
                  </button>

                  <button
                    v-if="!importResults"
                    type="button"
                    :disabled="!selectedFile || isUploading"
                    :class="[
                      'inline-flex justify-center cursor-pointer rounded border border-transparent px-4 py-2 text-sm font-medium text-white focus:outline-none',
                      !selectedFile || isUploading
                        ? 'bg-emerald-300 cursor-not-allowed'
                        : 'bg-emerald-600 hover:bg-emerald-700',
                    ]"
                    @click="uploadFile"
                  >
                    <Icon
                      v-if="isUploading"
                      icon="svg-spinners:180-ring"
                      class="w-5 h-5 mr-2"
                    />
                    {{ isUploading ? "Importing..." : "Import" }}
                  </button>

                  <button
                    v-if="importResults"
                    type="button"
                    class="inline-flex justify-center cursor-pointer rounded border border-transparent bg-emerald-600 px-4 py-2 text-sm font-medium text-white hover:bg-emerald-700 focus:outline-none"
                    @click="completeImport"
                  >
                    <Icon icon="mdi:check-circle" class="w-5 h-5 mr-2" />
                    Complete
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";

const props = defineProps({
  endpoint: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "",
  },
  description: {
    type: String,
    default:
      "Upload an Excel or CSV file to import data. The system will process and import valid entries.",
  },
  acceptedFileTypes: {
    type: String,
    default: ".xlsx, .xls, .csv",
  },
  maxFileSize: {
    type: Number,
    default: 5,
  },
  successMessage: {
    type: String,
    default: "Successfully imported data from {fileName}",
  },
  fileFieldName: {
    type: String,
    default: "file",
  },
});

const isOpen = ref(false);
const fileInput = ref<HTMLInputElement | null>(null);
const selectedFile = ref<File | null>(null);
const fileError = ref<string>("");
const isUploading = ref<boolean>(false);
const uploadProgress = ref<number>(0);
const statusMessage = ref<string>("");
const importResults = ref<{
  totalRecords: number;
  successful: number;
  failed: number;
  details?: any;
} | null>(null);

const expandedErrors = ref<Record<string, boolean>>({});

const emit = defineEmits(["import-success"]);

function openModal() {
  isOpen.value = true;
}

function closeModal() {
  isOpen.value = false;
  selectedFile.value = null;
  fileError.value = "";
  uploadProgress.value = 0;
  statusMessage.value = "";
  importResults.value = null;
  expandedErrors.value = {};
}

function toggleErrors(type: string) {
  expandedErrors.value[type] = !expandedErrors.value[type];
}

function triggerFileInput() {
  fileInput.value?.click();
}

function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];

    const acceptedTypes = props.acceptedFileTypes
      .split(",")
      .map((type) => type.trim().toLowerCase());
    const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();

    if (
      !acceptedTypes.some(
        (type) => type === fileExtension || type === file.type
      )
    ) {
      fileError.value = `Please select a valid file (${props.acceptedFileTypes})`;
      selectedFile.value = null;
      return;
    }

    if (file.size > props.maxFileSize * 1024 * 1024) {
      fileError.value = `File size should be less than ${props.maxFileSize}MB`;
      selectedFile.value = null;
      return;
    }

    fileError.value = "";
    selectedFile.value = file;
  }
}

function completeImport() {
  emit("import-success");
  closeModal();
}

async function uploadFile() {
  if (!selectedFile.value) return;

  isUploading.value = true;
  uploadProgress.value = 0;
  statusMessage.value = "Preparing to upload file...";
  importResults.value = null;

  try {
    const { $api, $toast } = useNuxtApp();

    const formData = new FormData();
    formData.append("file", selectedFile.value);

    const response = await $api.postWithProgress<any>(
      props.endpoint,
      formData,
      (progress: number, status: string) => {
        uploadProgress.value = progress;
        statusMessage.value = status;
      }
    );

    if (response.summary) {
      let totalRecords = 0;
      let successful = 0;
      let failed = 0;
      const importDetails: any = {};

      Object.keys(response.summary).forEach((key) => {
        const item = response.summary[key];
        totalRecords += item.total || 0;
        successful += item.created || 0;
        failed += item.failed || 0;

        if (item.total > 0) {
          importDetails[key] = {
            total: item.total,
            created: item.created,
            skipped: item.skipped,
            failed: item.failed,
            errors: item.errors || [],
          };
        }
      });

      importResults.value = {
        totalRecords,
        successful,
        failed,
        details: importDetails,
      };
    } else {
      importResults.value = {
        totalRecords: response.totalRecords || 0,
        successful: response.successCount || 0,
        failed: (response.totalRecords || 0) - (response.successCount || 0),
      };
    }

    statusMessage.value = "Import completed successfully!";
    uploadProgress.value = 100;

    const successMessage = props.successMessage.replace(
      "{fileName}",
      selectedFile.value.name
    );
    $toast.success(successMessage);
  } catch (error: any) {
    console.error("Error uploading file:", error);
    const { $toast } = useNuxtApp();

    statusMessage.value = `Error: ${error.message || "Failed to import data"}`;
    uploadProgress.value = 0;

    $toast.error(error.message || "Failed to import data. Please try again.");
  } finally {
    isUploading.value = false;
  }
}
</script>
