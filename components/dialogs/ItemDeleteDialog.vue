<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, DialogDescription, TransitionChild, TransitionRoot } from '@headlessui/vue';

interface Props {
    title?: string;
    description?: string;
    confirmButtonText?: string;
    cancelButtonText?: string;
    property?: {
        id?: number;
        name: string;
        endpoint: string;
    }
}

const isOpen = ref<boolean>(false);
const { $api, $toast }: any = useNuxtApp();
const loading = ref<boolean>(false);

const props = withDefaults(defineProps<Props>(), {
    title: 'Delete Confirmation',
    description: 'Are you sure you want to delete this item? This action cannot be undone.',
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel'
});

const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'confirm'): void;
}>();

const closeDialog = () => {
    isOpen.value = false;
    emit('close');
};

const confirmDelete = async() => {
    loading.value = true;
    await $api.delete(`${props.property?.endpoint}/${props.property?.id}`)
    .then(() => {
        emit('confirm');
    })
    .catch((error: any) => {
        console.error
        $toast.error("An errror occurred while deleting the item.");
    })
    .finally(() => {
        closeDialog();
        loading.value = false;
    });
};
</script>

<template>
    <button type="button" @click="isOpen = true" class="cursor-pointer">
        <Icon icon="mynaui:trash" class="w-6 h-6" />
    </button>
    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="closeDialog" class="relative z-50">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div class="flex min-h-full items-center justify-center p-4 text-center">
                    <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95">
                        <DialogPanel
                            class="w-full max-w-md transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all">
                            <SectionHeader  mode="error" icon="fluent-mdl2:report-warning" :title="title" description="Are you sure you want to delete this item? This action cannot be undone." />

                            <div class="mt-3">
                                <DialogDescription class="px-5 text-base text-black">
                                    {{ props.property?.name }} (1) item<span class="text-red-500">*</span>
                                </DialogDescription>
                            </div>

                            <div class="border-t border-gray-100 px-5 py-3 mt-3 flex justify-end space-x-3">
                                <button type="button"
                                    class="inline-flex cursor-pointer justify-center rounded border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-0 focus:ring-indigo-500 focus:ring-offset-2"
                                    @click="closeDialog">
                                    {{ cancelButtonText }}
                                </button>
                                <BaseButton  :loading="loading" :label="confirmButtonText" @click="confirmDelete" />
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>