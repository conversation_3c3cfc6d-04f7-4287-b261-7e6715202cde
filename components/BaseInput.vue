<script setup lang="ts">
import { computed, ref, watch, onMounted, inject } from 'vue';
import type { ValidationRules, FieldError, FormContext } from '@/types/input';

interface Props {
    modelValue: string | number | boolean | null | undefined;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    required?: boolean;
    readonly?: boolean;
    clearable?: boolean;
    step?: string;
    type?: 'text' | 'number' | 'password' | 'email' | 'tel' | 'url' | 'search' | 'date' | 'time' | 'datetime-local' | 'month' | 'week' | 'color' | 'file' | 'range' | 'textarea' | 'select' | 'radio' | 'checkbox';
    loading?: boolean;
    alignment?: 'start' | 'center' | 'end';
    multiple?: boolean;
    id?: string;
    keyOf?: string;
    iconStart?: string;
    iconEnd?: string;
    errorMessage?: string;
    name: string;
    rules?: ValidationRules;
    validateOnBlur?: boolean;
    validateOnChange?: boolean;
    validateOnMount?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    disabled: false,
    required: false,
    readonly: false,
    clearable: false,
    loading: false,
    alignment: 'start',
    multiple: false,
    id: () => `input-${Math.random().toString(36).substr(2, 9)}`,
    errorMessage: '',
    validateOnBlur: true,
    validateOnChange: false,
    validateOnMount: false
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
    (e: 'clear'): void;
    (e: 'blur'): void;
    (e: 'focus'): void;
    (e: 'validated', error: FieldError | undefined): void;
}>();

const formContext = inject<FormContext | null>('formContext', null);

const inputValue = computed({
    get: () => props.modelValue as string,
    set: (value: string) => {
        emit('update:modelValue', value);
        if (props.validateOnChange) {
            validate();
        }
    }
});

const error = ref<FieldError | undefined>(undefined);
const touched = ref(false);

const validate = () => {
    if (formContext) {
        formContext?.validateField(props.name);
        const fieldError = formContext.formErrors[props.name];
        error.value = fieldError;
        emit('validated', fieldError);
    } else {
        let fieldError: FieldError | undefined = undefined;
        
        if (props.required && !formContext) {
            fieldError = {
                type: 'required',
                message: props.errorMessage || 'This field is required'
            };
        }
        
        error.value = fieldError;
        emit('validated', fieldError);
    }
    
    return !error.value;
};

const handleBlur = () => {
    touched.value = true;
    emit('blur');
    if (props.validateOnBlur) {
        validate();
    }
};

const handleFocus = () => {
    emit('focus');
};

const handleClear = () => {
    emit('update:modelValue', '');
    emit('clear');
    if (props.validateOnChange) {
        validate();
    }
};

onMounted(() => {
    if (formContext && formContext.formValues[props.name] !== undefined) {
        emit('update:modelValue', formContext.formValues[props.name]);
    }
    
    if (props.validateOnMount) {
        validate();
    }
});

watch(() => inputValue.value, (newValue) => {
    if (formContext) {
        formContext.formValues[props.name] = newValue;
    }
});

watch(() => formContext?.formErrors[props.name], (newError) => {
    error.value = newError;
}, { immediate: true });

watch(() => props.errorMessage, (newMessage) => {
    if (newMessage && !error.value) {
        error.value = {
            type: 'custom',
            message: newMessage
        };
    } else if (!newMessage && error.value?.type === 'custom') {
        error.value = undefined;
    }
}, { immediate: true });

const errorToDisplay = computed(() => {
    return error.value?.message || '';
});
</script>

<template>
    <div class="flex flex-col gap-1.5">
        <label v-if="label" :for="id" class="block text-base font-medium text-gray-700">
            {{ label }}
            <span v-if="required" class="text-red-500">*</span>
        </label>

        <div class="relative flex items-center">
            <span v-if="iconStart" class="absolute left-2 text-gray-400">
                <Icon :icon="iconStart" class="w-5 h-5"></Icon>
            </span>

            <input  
                v-model="inputValue" 
                v-bind:key="keyOf"
                :placeholder="placeholder" 
                :type="type" 
                :disabled="disabled"
                :readonly="readonly" 
                :required="required" 
                :aria-label="!label ? placeholder : undefined" 
                :aria-invalid="!!error"
                :aria-errormessage="`error-${id}`"
                :step="step"
                @blur="handleBlur"
                @focus="handleFocus"
                class="w-full px-3 py-1 border border-gray-200 text-base
                placeholder-gray-400 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500 
                focus:border-emerald-500 disabled:bg-gray-100 disabled:text-gray-500
                text-gray-900 transition duration-200" 
                :class="{
                    'pl-10': iconStart,
                    'pr-10': iconEnd || clearable || loading,
                    'text-center': alignment === 'center',
                    'text-right pr-8': alignment === 'end',
                    'border-red-500 focus:ring-red-500 focus:border-red-500': error,
                }" 
            />

            <span v-if="iconEnd" class="absolute right-2 text-gray-400">
                <Icon :icon="iconEnd" class="w-5 h-5"></Icon>
            </span>

            <button v-if="clearable && !disabled && !readonly && modelValue" @click="handleClear" 
                class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 
                hover:text-gray-600 focus:outline-none" 
                type="button">
                <span class="sr-only">Clear input</span>
                <Icon icon="proicons:cancel-circle" class="w-5 h-5" />
            </button>

            <div v-if="loading" class="absolute right-2 top-1/2 -translate-y-1/2">
                <div class="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-emerald-500" />
            </div>
        </div>

        <div v-if="errorToDisplay" class="flex items-center mt-1" :id="`error-${id}`">
            <Icon icon="circum:warning" class="w-4 h-4 text-red-600 mr-2"/>
            <p class="text-sm text-red-600">{{ errorToDisplay }}</p>
        </div>
    </div>
</template>