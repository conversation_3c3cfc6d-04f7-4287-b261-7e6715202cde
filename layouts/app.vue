<template>
    <NuxtLoadingIndicator color="#16a34a" :height="3" />
    <div class="min-h-screen bg-gray-50">

        <div class="flex h-screen">
            <aside
                class="bg-gray-100 border-r border-gray-200 transform transition-all duration-300 fixed z-[1000] h-full md:static"
                :class="[
                    isSidebarOpen ? 'translate-x-0' : '-translate-x-full',
                    isMiniVariant ? 'md:w-16' : 'md:w-64 w-64'
                ]">
                <nav class="p-2 space-y-1">
                    <div class="flex items-center justify-center space-x-4">
                        <div
                            :class="!isMiniVariant ? 'w-full flex flex-col items-start space-y-2 px-3 py-3' : 'w-full flex flex-col items-center space-y-2 py-3'">
                            <img src="@/assets/icons/icon.png" alt="malawi-coat-of-arms"
                                :class="!isMiniVariant ? 'h-20 w-20' : 'w-10 h-10'" />
                            <h3 v-if="!isMiniVariant" class="text-2xl font-semibold uppercase">Malawi Test Catalog</h3>
                        </div>
                    </div>

                    <!-- <div class="my-4" v-if="!isMiniVariant">
                        <div class="w-full relative bg-white rounded border border-gray-200">
                            <input placeholder="Search..." class="w-full focus:outline-none focus:ring-0 px-8 py-1.5" />
                            <div class="absolute top-2 left-2">
                                <Icon icon="mynaui:search" class="h-5 w-5 text-zinc-500" />
                            </div>
                        </div>
                    </div> -->

                    <NuxtLink v-for="item in navigationItems" :key="item.path" :to="item.path"
                        class="flex items-center text-lg font-medium py-1.5 px-2 rounded nonehover:bg-gray-100 transition-colors duration-200 overflow-hidden whitespace-nowrap"
                        :class="{ 'bg-emerald-700 text-emerald-100': isCurrentRoute(item.path), 'px-0': isMiniVariant }"
                        :title="isMiniVariant ? item.name : ''">
                        <Icon :icon="item.icon" class="h-8 w-8 flex-shrink-0" :class="{ 'mr-3': !isMiniVariant }" />
                        <span class="transition-opacity duration-200" :class="{ 'opacity-0': isMiniVariant }">
                            {{ item.name }}
                        </span>
                    </NuxtLink>
                </nav>
                <div class="absolute mx-4 rounded p-2 flex items-center bottom-0 right-0 left-0">
                    <Icon icon="devicon-plain:git" class="w-5 h-5 text-emerald-600" />
                    <div v-if="!isMiniVariant" class="text-base font-thin text-emerald-600 ml-2">
                        {{ Package.version }}
                    </div>
                </div>
            </aside>

            <div class="flex flex-col flex-1">
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center justify-between px-4 h-16">
                        <div class="flex items-center space-x-4">
                            <!-- Mobile sidebar toggle -->
                            <button @click="toggleSidebar" class="md:hidden cursor-pointer hover:bg-gray-100 p-2 rounded-lg transition-colors duration-200">
                                <Icon icon="material-symbols-light:menu-rounded" class="h-6 w-6 text-gray-600" />
                            </button>
                            <!-- Desktop mini variant toggle -->
                            <button @click="toggleMiniVariant" class="hidden md:block cursor-pointer hover:bg-gray-100 p-2 rounded-lg transition-colors duration-200">
                                <Icon icon="material-symbols-light:menu-rounded" class="h-6 w-6 text-gray-600" />
                            </button>

                            <!-- Page title and breadcrumb -->
                            <div class="hidden sm:block">
                                <h1 class="text-xl font-semibold text-gray-900">{{ getPageTitle() }}</h1>
                                <nav class="flex text-sm text-gray-500" aria-label="Breadcrumb">
                                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                        <li class="inline-flex items-center">
                                            <NuxtLink to="/app" class="hover:text-emerald-600 transition-colors duration-200">
                                                Dashboard
                                            </NuxtLink>
                                        </li>
                                        <li v-if="getBreadcrumb()" class="flex items-center">
                                            <Icon icon="heroicons:chevron-right-20-solid" class="w-4 h-4 mx-1" />
                                            <span class="text-gray-700">{{ getBreadcrumb() }}</span>
                                        </li>
                                    </ol>
                                </nav>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">

                            <div class="hidden sm:block">
                                <QuickActions />
                            </div>
                            
                            <UserMenu />
                        </div>
                    </div>
                </header>

                <div
                    v-if="isSidebarOpen && windowWidth < 768"
                    class="fixed inset-0 bg-emerald-900 opacity-25 z-[999]"
                    @click="toggleSidebar">
                </div>

                <main class="flex-1 overflow-y-auto bg-gray-50 relative">
                    <slot />
                </main>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import Package from "@/package.json";

definePageMeta({
    middleware: ['auth']
});

interface NavigationItem {
    name: string;
    path: string;
    icon: string;
}

const navigationItems: NavigationItem[] = [
    {
        name: 'Dashboard',
        path: '/app',
        icon: 'material-symbols-light:space-dashboard-outline'
    },
    {
        name: 'Specimens',
        path: '/app/specimens',
        icon: 'healthicons:microscope-with-specimen-outline'
    },
    {
        name: 'Test Types',
        path: '/app/test-types',
        icon: 'healthicons:test-tubes-outline'
    },
    {
        name: 'Test Panels',
        path: '/app/test-panels',
        icon: 'fluent:panel-left-text-20-regular'
    },
    {
        name: 'Drugs',
        path: '/app/drugs',
        icon: 'healthicons:medicines-outline'
    },
    {
        name: 'Organisms',
        path: '/app/organisms',
        icon: 'healthicons:virus-outline'
    },
    {
        name: 'Products',
        path: '/app/products',
        icon: 'eos-icons:products-outlined'
    },
    {
        name: 'Equipments',
        path: '/app/equipments',
        icon: 'fluent:box-20-regular'
    },
    {
        name: 'Departments',
        path: '/app/departments',
        icon: 'circum:hospital-1'
    },
    {
        name: 'Accounts',
        path: '/app/users',
        icon: 'mdi:account-multiple-outline'
    },
    {
        name: 'Versions',
        path: '/app/version',
        icon: 'devicon-plain:git'
    },
];

const isSidebarOpen = ref<boolean>(true);
const isMiniVariant = ref<boolean>(false);
const currentTheme = ref<string>('light');
const route = useRoute();
const colorMode = useColorMode();

const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light';
    colorMode.preference = currentTheme.value;
}

const toggleMiniVariant = (): void => {
    isMiniVariant.value = !isMiniVariant.value;
};

const toggleSidebar = (): void => {
    isSidebarOpen.value = !isSidebarOpen.value;
};

const isCurrentRoute = (path: string): boolean => {
    return `/${route.path.split('/').splice(1, 2).join("/")}` === path;
};

const getPageTitle = (): string => {
    const pathSegments = route.path.split('/').filter(segment => segment);
    if (pathSegments.length <= 1) return 'Dashboard';

    const lastSegment = pathSegments[pathSegments.length - 1];
    return lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1).replace(/-/g, ' ');
};

const getBreadcrumb = (): string | null => {
    const pathSegments = route.path.split('/').filter(segment => segment);
    if (pathSegments.length <= 2) return null;

    const segment = pathSegments[2];
    return segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
};

// Handle responsive behavior
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024);

const updateWidth = () => {
    windowWidth.value = window.innerWidth;
    if (windowWidth.value < 768) { // md breakpoint is typically 768px
        isSidebarOpen.value = false;
    } else {
        isSidebarOpen.value = true;
    }
};

// Add event listener for resize
onMounted(() => {
    window.addEventListener('resize', updateWidth);
    updateWidth(); // Set initial state
});

onUnmounted(() => {
    window.removeEventListener('resize', updateWidth);
});
</script>

<style>
:root {
    --dp-font-family: "Avenir Next Cyr";
}

.datatable {
    --easy-table-header-font-size: 17px;
    --easy-table-body-row-font-size: 15px;
    --easy-table-footer-font-size: 15px;
}

/* Make tables responsive on mobile */
@media (max-width: 768px) {
    .datatable {
        --easy-table-header-font-size: 14px;
        --easy-table-body-row-font-size: 13px;
        --easy-table-footer-font-size: 13px;
    }

    .datatable .vue3-easy-data-table__body {
        overflow-x: auto;
    }
}

.datepicker {
    font-family: "Avenir Next Cyr";
    --dp-primary-color: green !important;
    --dp-border: none !important;
    --dp-primary-color: green;

    &:hover {
        border-color: #ececec;
    }
}

.dp__theme_light {
    --dp-background-color: #fff;
    --dp-text-color: #212121;
    --dp-hover-color: oklch(0.508 0.118 165.612);
    --dp-hover-text-color: white;
    --dp-hover-icon-color: #959595;
    --dp-primary-color: oklch(0.508 0.118 165.612);
    --dp-primary-disabled-color: oklch(0.508 0.118 165.612);
    --dp-primary-text-color: #f8f5f5;
    --dp-secondary-color: #c0c4cc;
    --dp-border-color: #fff;
    --dp-menu-border-color: #ddd;
    --dp-border-color-hover: #fff;
    --dp-border-color-focus: #fff;
    --dp-disabled-color: #f6f6f6;
    --dp-scroll-bar-background: #f3f3f3;
    --dp-scroll-bar-color: #959595;
    --dp-success-color: #76d275;
    --dp-success-color-disabled: #a3d9b1;
    --dp-icon-color: #959595;
    --dp-danger-color: #ff6f60;
    --dp-marker-color: #ff6f60;
    --dp-tooltip-color: #fafafa;
    --dp-disabled-color-text: #8e8e8e;
    --dp-highlight-color: rgb(25 118 210 / 10%);
    --dp-range-between-dates-background-color: var(--dp-hover-color, oklch(0.508 0.118 165.612));
    --dp-range-between-dates-text-color: var(--dp-hover-text-color, oklch(0.508 0.118 165.612));
    --dp-range-between-border-color: var(--dp-hover-color, oklch(0.508 0.118 165.612));
}
</style>
