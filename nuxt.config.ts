import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: [
    '@nuxtjs/color-mode',
    '~/modules/api',
    '@pinia/nuxt',
  ],
  ssr: false,
  app: {
    head: {
      title: 'Malawi Test Catalog',
      link: [
        { rel: 'icon', type: 'image/png', href: '/Coat_of_arms_of_Malawi.svg.png' }
      ],
      meta: [
        { name: 'description', content: 'Malawi Test Catalog - A comprehensive catalog of laboratory tests, diagnostics, and screening services' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' }
      ]
    },
    pageTransition: false
  },
  colorMode: {
    preference: 'system',
    fallback: 'light',
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },
  runtimeConfig: {
    public: {
      apiBaseUrl: process.env.API_BASE_URL,
    },
  },
  css: [
    '~/assets/css/main.css',
    '~/assets/css/fonts.css',
  ],
})
