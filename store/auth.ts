import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Credentials } from '@/types/form';

interface LoginResponse {
    data: {
        token: string;
    },
    user: {
        username: string;
        app_id: string;
        app_uuid?: string;
    },
    message: string;
}

export const useAuthStore = defineStore('auth', () => {
    const token = ref<string | null>(null);
    const isLoading = ref(false);
    const user = ref<{ username: string; app_id: string; app_uuid?: string } | null>(null);
    const error = ref<string | null>(null);
    const { $api } = useNuxtApp();

    const initializeFromStorage = (): void => {
        if (import.meta.client) {
            const storedToken = sessionStorage.getItem('auth_token');
            const storedUser = sessionStorage.getItem('auth_user');

            if (storedToken) token.value = storedToken;
            if (storedUser) user.value = JSON.parse(storedUser);
        }
    };

    initializeFromStorage();

    const isAuthenticated = computed(() => !!token.value);

    const setToken = (newToken: string) => {
        token.value = newToken;
        sessionStorage.setItem('auth_token', newToken);
    };

    const clearToken = () => {
        token.value = null;
        sessionStorage.removeItem('auth_token');
    };

    const login = async (credentials: Credentials, keepSignedIn: boolean) => {
        isLoading.value = true;
        error.value = null;

        try {
            const response: LoginResponse = await $api.post(`login`, {
                username: credentials.username,
                password: credentials.password,
            });

            if (response.message && response.message.toLowerCase().includes('wrong')) {
                error.value = response.message;
                return { success: false, error: error.value };
            }

            token.value = response.data.token;
            const { app_uuid, ...genericUser } = response.user;
            const userToStore = keepSignedIn ? response.user : genericUser;

            user.value = userToStore;

            sessionStorage.setItem('auth_user', JSON.stringify(userToStore));
            sessionStorage.setItem('auth_token', String(token.value));

            return { success: true };
        } catch (err: any) {
            console.error(err);
            error.value = 'Authentication failed';
            return { success: false, error: error.value };
        } finally {
            isLoading.value = false;
        }
    };

    const logout = async (): Promise<{ success: boolean;}> => {
        isLoading.value = true;
        try {
            token.value = null;
            user.value = null;

            if (import.meta.client) {
                sessionStorage.removeItem('auth_token');
                sessionStorage.removeItem('auth_user');
            }

            clearToken();

            return { success: true };
        } catch (err) {
            console.error('Logout error:', err);
            token.value = null;
            if (import.meta.client) {
                sessionStorage.removeItem('auth_token');
            }

            return { success: true };
        } finally {
            isLoading.value = false;
        }
    };

    return {
        token,
        user,
        isLoading,
        error,
        isAuthenticated,
        login,
        logout,
        setToken,
        clearToken
    };
});
