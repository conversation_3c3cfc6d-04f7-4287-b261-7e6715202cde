import { defineNuxtPlugin } from '#app';
import type { Metadata } from '@/types/metadata';

export default defineNuxtPlugin((nuxtApp) => {
    const createMetadata = (): Metadata => {
        const getApi = () => nuxtApp.$api

        return {
            drugs: {
                getAll: (params = {}) => (getApi() as any).get('drugs', params),
                getById: (id: number) => (getApi() as any).get(`drugs/${id}`),
                create: async (data: any, options: any) => (getApi() as any).post('drugs', data, options),
                update: async (id, data, options) => (getApi() as any).put(`drugs/${id}`, data, options),
                delete: async (id) => (getApi() as any).delete(`drugs/${id}`)
            },
            organisms: {
                getAll: (params = {}) => (getApi() as any).get('organisms', params),
                getById: (id: number) => (getApi() as any).get(`organisms/${id}`),
                create: async (data: any) => (getApi() as any).post('organisms', data),
                update: async (id, data) => (getApi() as any).put(`organisms/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`organisms/${id}`)
            },
            test_statuses: {
                getAll: (params = {}) => (getApi() as any).get('test_statuses', params),
                getById: (id: number) => (getApi() as any).get(`test_statuses/${id}`),
                create: async (data: any) => (getApi() as any).post('test_statuses', data),
                update: async (id, data) => (getApi() as any).put(`test_statuses/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`test_statuses/${id}`)
            },
            departments: {
                getAll: (params = {}) => (getApi() as any).get('departments', params),
                getById: (id: number) => (getApi() as any).get(`departments/${id}`),
                create: async (data: any) => (getApi() as any).post('departments', data),
                update: async (id, data) => (getApi() as any).put(`departments/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`departments/${id}`)
            },
            specimen_types: {
                getAll: (params = {}) => (getApi() as any).get('specimen_types', params),
                getById: (id: number) => (getApi() as any).get(`specimen_types/${id}`),
                create: async (data: any) => (getApi() as any).post('specimen_types', data),
                update: async (id, data) => (getApi() as any).put(`specimen_types/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`specimen_types/${id}`)
            },
            measure_types: {
                getAll: (params = {}) => (getApi() as any).get('test_types/measure_types', params),
                getById: (id: number) => (getApi() as any).get(`test_types/measure_types/${id}`),
                create: async (data: any) => (getApi() as any).post('test_types/measure_types', data),
                update: async (id, data) => (getApi() as any).put(`test_types/measure_types/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`test_types/measure_types/${id}`)
            },
            lab_test_sites: {
                getAll: (params = {}) => (getApi() as any).get('lab_test_sites', params),
                getById: (id: number) => (getApi() as any).get(`lab_test_sites/${id}`),
                create: async (data: any) => (getApi() as any).post('lab_test_sites', data),
                update: async (id, data) => (getApi() as any).put(`lab_test_sites/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`lab_test_sites/${id}`)
            },
            equipments: {
                getAll: (params = {}) => (getApi() as any).get('equipments', params),
                getById: (id: number) => (getApi() as any).get(`equipments/${id}`),
                create: async (data: any) => (getApi() as any).post('equipments', data),
                update: async (id, data) => (getApi() as any).put(`equipments/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`equipments/${id}`)
            },
            products: {
                getAll: (params = {}) => (getApi() as any).get('products', params),
                getById: (id: number) => (getApi() as any).get(`products/${id}`),
                create: async (data: any) => (getApi() as any).post('products', data),
                update: async (id, data) => (getApi() as any).put(`products/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`products/${id}`)
            },
            test_panels: {
                getAll: (params = {}) => (getApi() as any).get('test_panels', params),
                getById: (id: number) => (getApi() as any).get(`test_panels/${id}`),
                create: async (data: any) => (getApi() as any).post('test_panels', data),
                update: async (id, data) => (getApi() as any).put(`test_panels/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`test_panels/${id}`)
            },
            users: {
                getAll: (params = {}) => (getApi() as any).get('users', params),
                getById: (id: number) => (getApi() as any).get(`users/${id}`),
                create: async (data: any) => (getApi() as any).post('users', data),
                update: async (id, data) => (getApi() as any).put(`users/${id}`, data),
                delete: async (id) => (getApi() as any).delete(`users/${id}`)
            }
                
        }
    }

    const metadata = createMetadata()

    return {
        provide: {
            metadata
        }
    }
})