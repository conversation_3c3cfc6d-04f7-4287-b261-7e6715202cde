<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full">
      <div class="text-center mb-8 p-8">
        <div class="flex flex-col items-center">
          <img src="/Coat_of_arms_of_Malawi.svg.png" alt="malawi-coat-of-arms" class="w-auto h-28" />
          <h2 class="text-2xl font-semibold text-gray-900 uppercase mt-3">
            Malawi Test Catalog
          </h2>
          <p class="text-gray-700 mt-2 text-lg">
            A comprehensive catalog of laboratory tests, diagnostics, and screening services
          </p>
        </div>
      </div>

      <BaseForm :config="formConfig" @submit="handleSubmit"
        class="space-y-6 px-8 py-5 bg-white mx-5 border rounded border-gray-200">
        <div>
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-2xl font-semibold">Sign in to continue</h3>
          </div>

          <div class="space-y-4">
            <BaseInput name="username" icon-start="proicons:person-2" label="Username" v-model="username"
              placeholder="Enter your username" clearable />
            <BaseInput name="password" icon-start="proicons:lock" label="Password" type="password" v-model="password"
              placeholder="Enter your password" clearable />
          </div>
        </div>

        <div class="flex items-center">
          <input v-model="keepSignedIn" id="keep-signed-in" type="checkbox"
            class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded" />
          <label for="keep-signed-in" class="ml-2 block text-gray-900">
            Keep me signed in
          </label>
        </div>

        <div v-if="authStore.error" class="text-red-500 text-sm">
          {{ authStore.error }}
        </div>

        <BaseButton label="Sign in" :full-width="true" type="submit" :loading="authStore.isLoading" />
      </BaseForm>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { useAuthStore } from "@/store/auth";
import type { FormConfig } from "@/types/input";

// Set page title and metadata
useHead({
  title: 'Login - Malawi Test Catalog',
  meta: [
    { name: 'description', content: 'Login to Malawi Test Catalog system' }
  ]
});

const username = ref<string>("");
const password = ref<string>("");
const keepSignedIn = ref<boolean>(false);
const authStore = useAuthStore();
const router = useRouter();
const { $toast } = useNuxtApp();
const { start, finish } = useLoadingIndicator();

const formConfig: FormConfig = {
  fields: [
    { name: "username", label: "username", rules: { required: true } },
    { name: "password", label: "password", rules: { required: true } },
  ],
};

const handleSubmit = async (values: any, valid: boolean): Promise<void> => {
  start();
  if (valid) {
    const result = await authStore.login(values, keepSignedIn.value);
    if (result.success) {
      $toast.success("Login successfully!");
      setTimeout(() => {
        router.push("/app");
      }, 2000)
    }
  }
  finish();
};

watch(() => authStore.error, (error) => {
  if (error) {
    $toast.error(error);
  }
});

</script>
