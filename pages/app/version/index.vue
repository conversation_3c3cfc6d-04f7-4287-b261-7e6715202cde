<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="w-full bg-white rounded border border-gray-100">
      <div class="rounded-t p-3 flex items-center space-x-2">
        <Icon icon="devicon-plain:git" class="w-6 h-6 text-black" />
        <h1 class="text-3xl font-semibold text-black">Versions</h1>
      </div>
      <div
        class="w-full flex items-center justify-between p-4 border-b border-gray-200"
      >
        <div>
          <BaseDropdown
            name="versions-dropdown"
            :options="
              versions.map((version) => ({
                id: version.id,
                label: version.version,
              }))
            "
            v-model="selectedVersion"
            label="Select version"
          />
        </div>
        <div class="flex items-center space-x-4">
          <VersionReleaseDialog @confirm="confirmRelease" />
        </div>
        <VersionConfirmReleaseDialog
          v-model="isConfirmDialogOpen"
          :version="confirmedVersion"
          @confirm="onConfirmRelease"
        />
      </div>

      <div class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-2xl font-semibold text-gray-800">
            Version {{ currentSelectedVersion?.version }}
          </h2>
          <div class="flex items-center space-x-2">
            <span
              class="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium"
            >
              Released:
              {{
                dayjs(currentSelectedVersion?.created_at).format(DATE_FORMAT)
              }}
            </span>
            <span
              class="px-3 py-1 bg-sky-100 text-sky-500 rounded-full text-sm font-medium"
            >
              {{ getVersionStatus(selectedVersion.id) }}
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-6 mb-6 sm:grid-cols-2 lg:grid-cols-4">
          <DashboardStatisticsCard v-for="metric in metrics" :data="metric" />
        </div>

        <div class="mb-6">
          <h3 class="text-xl font-semibold mb-3">Changelog</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="(change, index) in currentSelectedVersion?.version_details
                .releaseNotes"
              :key="index"
              class="p-4 bg-white border rounded border-gray-200"
            >
              <div class="flex items-start mb-2">
                <div
                  class="rounded-full justify-center mr-3 w-8 h-8 flex items-center"
                  :class="getChangeTypeColor(change.changeType)"
                >
                  <span class="text-white text-lg font-medium">{{
                    change.changeType.charAt(0).toUpperCase()
                  }}</span>
                </div>
                <div>
                  <h4 class="font-medium text-black text-lg">
                    {{ change.title }}
                  </h4>
                  <span class="text-xs text-gray-500">{{
                    change.created_at
                  }}</span>
                </div>
              </div>
              <p class="text-base text-gray-500">{{ change.description }}</p>
              <div
                class="mt-3 text-xs font-medium"
                :class="getChangeTypeTextColor(change.changeType)"
              >
                {{ change.changeType }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import type { Version } from "@/types";
import type { Option } from "@/types/form";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Versions | Malawi Test Catalog",
});

const selectedVersion = ref({ id: 0, label: "click to select" });
const versions = ref<{ id: number; created_at: string; version: string }[]>([]);

const { $api, $toast } = useNuxtApp();
const isNewReleaseDialogOpen = ref(false);
const isConfirmDialogOpen = ref(false);
const confirmedVersion = ref();
const currentSelectedVersion = ref<Version>();

const getVersionStatus = (versionId: number) => {
  const statuses = {
    1: "Current",
    2: "Stable",
    3: "Legacy",
    4: "Legacy",
    5: "Legacy",
  };
  return statuses[versionId as keyof typeof statuses] || "Unknown";
};

const metrics = computed(() => {
  return [
    {
      title: "Specimens",
      count: currentSelectedVersion.value?.catalog?.specimen_types.length || 0,
      icon: "healthicons:microscope-with-specimen-outline",
      description: "Total number of specimens in the catalog",
    },
    {
      title: "Test Types",
      count: currentSelectedVersion.value?.catalog?.test_types.length || 0,
      icon: "healthicons:test-tubes-outline",
      description: "Total number of test types in the catalog",
    },
    {
      title: "Drugs",
      count: currentSelectedVersion.value?.catalog?.drugs.length || 0,
      icon: "healthicons:medicines-outline",
      description: "Total number of drugs in the catalog",
    },
    {
      title: "Organisms",
      count: currentSelectedVersion.value?.catalog?.organisms.length || 0,
      icon: "healthicons:virus-outline",
      description: "Total number of organisms in the catalog",
    },
  ];
});

const getChangeTypeColor = (type: string) => {
  const colors = {
    feature: "bg-green-500",
    improvement: "bg-blue-500",
    bugfix: "bg-yellow-500",
    security: "bg-red-500",
  };
  return colors[type as keyof typeof colors] || "bg-gray-500";
};

const getChangeTypeTextColor = (type: string) => {
  const colors = {
    feature: "text-green-600",
    improvement: "text-blue-600",
    bugfix: "text-yellow-600",
    security: "text-red-600",
  };
  return colors[type as keyof typeof colors] || "text-gray-600";
};

const confirmRelease = (values: any) => {
  isNewReleaseDialogOpen.value = false;
  isConfirmDialogOpen.value = true;
  confirmedVersion.value = values;
};

const fetchCatalogByVersion = async (versionId: string): Promise<void> => {
  try {
    const response: any = await $api.get(
      `retrieve_test_catalog?version=${versionId}`
    );
    if (response) {
      currentSelectedVersion.value = response;
    }
  } catch (error) {
    console.error("Error fetching catalog by version:", error);
  }
};

const fetchVersions = async (): Promise<void> => {
  try {
    const response: any = await $api.get("retrieve_test_catalog_versions");
    if (response) {
      versions.value = response;
      selectedVersion.value = {
        id: response[0].id,
        label: response[0].version,
      };
      fetchCatalogByVersion(response[0].version);
    }
  } catch (error) {
    console.error("Error fetching versions:", error);
  }
};

const onConfirmRelease = (value: any): void => {
  $toast.success(`Version ${value.version} released successfully!`);
  fetchVersions();
};

watch(
  () => selectedVersion.value,
  (newVersion: Option) => {
    if (newVersion) {
      const versionId: any = newVersion;
      const getVersionLabel = computed(() => {
        const version = versions.value.find((v) => v.id === versionId);
        return version ? version.version : "Unknown";
      });
      fetchCatalogByVersion(getVersionLabel.value);
    }
  },
  { deep: true }
);

onMounted(() => {
  fetchVersions();
});
</script>
