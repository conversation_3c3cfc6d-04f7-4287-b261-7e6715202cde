<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="healthicons:virus-outline"
          title="Create New Organism"
          description="Add details of a new organism"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="organism.name"
                  required
                />
                <BaseInput
                  name="short_name"
                  label="Short name"
                  v-model="organism.short_name"
                />
                <BaseInput
                  name="preferred_name"
                  label="Preferred name"
                  v-model="organism.preferred_name"
                  required
                />
                <BaseInput
                  name="scientific_name"
                  label="Scientific name"
                  v-model="organism.scientific_name"
                />
                <BaseInput
                  name="lonic_code"
                  label="LONIC code"
                  v-model="organism.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="organism.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="organism.description"
                      contentType="html"
                    >
                    </RichTextEditor>
                  </div>
                </div>
                <BaseDropdown
                  name="drug"
                  label="Drug(s)"
                  is-multiple
                  v-model="organism.drugs"
                  :options="
                    drugs.map((drug) => {
                      return { ...drug, label: drug.name };
                    })
                  "
                  required
                />
              </div>
              <BaseButton
                class="mt-4"
                label="Submit"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Drug, Organism } from "@/types";
import type { FormConfig } from "@/types/input";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Create Organism | Malawi Test Catalog",
});

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const organism = ref<Organism>({
  name: "",
  short_name: "",
  description: "",
  id: 0,
  created_at: "",
  updated_at: "",
  moh_code: null,
  nlims_code: null,
  loinc_code: null,
  preferred_name: null,
  scientific_name: null,
  drugs: [],
});
const drugs = ref<Drug[]>([]);
const formConfig: FormConfig = {
  fields: [
    {
      name: "name",
      label: "Name",
      rules: { required: true },
    },
    {
      name: "short_name",
      label: "Short name",
      rules: { required: false },
    },
    {
      name: "preferred_name",
      label: "Preferred name",
      rules: { required: true },
    },
    {
      name: "scientific_name",
      label: "Scientific name",
      rules: { required: false },
    },
    {
      name: "lonic_code",
      label: "LONIC code",
      rules: { required: false },
    },
    {
      name: "moh_code",
      label: "MOH code",
      rules: { required: false },
    },
    {
      name: "drug",
      label: "Drug",
      rules: {
        custom: (value: any) =>
          (value && value.length > 0) || "Please select at least one drug",
      },
    },
  ],
};

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (valid) {
    loading.value = true;
    try {
      const response = await $metadata.organisms.create(organism.value);
      if (response) {
        $toast.success("Organism created successfully");
        setTimeout(() => {
          navigateTo({ path: "/app/organisms" });
        }, 3000);
      }
    } catch (error: any) {
      $toast.error("An error occurred while creating organism");
    } finally {
      loading.value = false;
    }
  }
};

onMounted(async () => {
  const data: any = await $metadata.drugs.getAll();
  drugs.value = data;
});
</script>
