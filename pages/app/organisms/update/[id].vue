<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="healthicons:virus-outline"
          title="Update organism"
          description="Modify details of the organism"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="formData.name"
                  required
                />
                <BaseInput
                  name="short_name"
                  label="Short name"
                  v-model="formData.short_name"
                />
                <BaseInput
                  name="preferred_name"
                  label="Preferred name"
                  v-model="formData.preferred_name"
                  required
                />
                <BaseInput
                  name="scientific_name"
                  label="Scientific name"
                  v-model="formData.scientific_name"
                />
                <BaseInput
                  name="loinc_code"
                  label="LONIC code"
                  v-model="formData.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="formData.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="formData.description"
                      contentType="html"
                    ></RichTextEditor>
                  </div>
                </div>
                <BaseDropdown
                  name="drug"
                  label="Drug(s)"
                  is-multiple
                  v-model="formData.drugs"
                  :options="
                    drugs.map((drug) => {
                      return { ...drug, label: drug.name };
                    })
                  "
                  required
                />
              </div>
              <BaseButton
                :disabled="!form.isValid"
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Drug, Organism } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Update Organism | Malawi Test Catalog",
});

const { $metadata, $toast } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const organism = ref<Organism | null>(null);
const drugs = ref<Drug[]>([]);
const formData = reactive({
  name: "",
  short_name: "",
  description: "",
  preferred_name: "",
  scientific_name: "",
  loinc_code: "",
  moh_code: "",
  nlims_code: "",
  id: 0,
  created_at: "",
  updated_at: "",
  drugs: [],
});

const formConfig: FormConfig = {
  fields: [
    { name: "name", label: "Name", rules: { required: true } },
    { name: "short_name", label: "Short name", rules: { required: false } },
    {
      name: "preferred_name",
      label: "Preferred name",
      rules: { required: true },
    },
    {
      name: "scientific_name",
      label: "Scientific name",
      rules: { required: false },
    },
    { name: "loinc_code", label: "LONIC code", rules: { required: false } },
    { name: "moh_code", label: "MOH code", rules: { required: false } },
  ],
};

const fetchOrganism = async () => {
  const id = route.params.id;
  if (!id) {
    $toast.error("Invalid organism ID");
    setTimeout(() => {
      router.push("/app/organisms");
    }, 3000);
    return;
  }

  loading.value = true;
  try {
    const response: any = await $metadata.organisms.getById(Number(id));
    organism.value = response;

    if (organism.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          organism.value &&
          key in organism.value &&
          organism.value[key as keyof Organism] !== undefined
        ) {
          (formData as any)[key] = organism.value[key as keyof Organism];
          (formData as any).drugs = organism.value?.drugs?.map(
            (drug: any) => drug.id
          );
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch organism data");
    setTimeout(() => {
      router.push("/app/organisms");
    }, 3000);
  } finally {
    loading.value = false;
  }
};

onMounted(fetchOrganism);

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (!valid || !organism.value) return;

  loading.value = true;
  try {
    const updatedOrganism = {
      ...organism.value,
      ...formData,
    };

    await $metadata.organisms.update(updatedOrganism.id, updatedOrganism);
    $toast.success("Organism updated successfully");
    setTimeout(() => {
      router.push("/app/organisms");
    }, 3000);
  } catch (error) {
    $toast.error("An error occurred while updating organism");
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  const data: any = await $metadata.drugs.getAll();
  drugs.value = data;
});
</script>
