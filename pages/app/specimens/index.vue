<template>
  <div class="px-4 py-8">
    <div class="">
      <h1 class="text-3xl font-semibold mb-6">Specimens</h1>

      <div class="w-full bg-white p-2 border border-gray-100 rounded my-2 px-2">
        <div
          class="w-full flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3"
        >
          <!-- <div
            class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:pr-2 w-full"
          >
            <p><span class="text-lg font-medium">Date created:</span></p>
            <div class="w-full sm:ml-2">
              <Datepicker
                required
                position="left"
                placeholder="select start & end date"
                :range="true"
                input-class-name="datepicker w-full"
                v-model="dateRange"
                :format="DATE_PICKER"
                :maxDate="new Date()"
              />
            </div>
          </div> -->
        </div>
      </div>

      <div
        class="w-full flex flex-col md:flex-row items-start md:items-center justify-between gap-4 mb-4"
      >
        <div class="flex items-center space-x-4 w-full md:w-auto">
          <button
            @click="$router.push('/app/specimens/create')"
            class="cursor-pointer bg-emerald-600 hover:bg-emerald-700 transition duration-150 flex items-center text-emerald-100 px-2.5 py-2 rounded w-full md:w-auto"
          >
            <Icon icon="mdi:plus" class="w-5 h-5 mr-1" />
            Add new
          </button>
          <BaseExcelExport :export-data="exportData" />
        </div>
        <div class="max-w-md w-full">
          <BaseSearchbar
            v-model="search"
            placeholder="Search..."
            clearable
            class="w-full md:w-auto"
          />
        </div>
      </div>
    </div>
    <div class="mt-5">
      <datatable
        table-class-name="datatable"
        :headers="headers"
        :items="filteredSpecimens"
        v-model:items-selected="itemsSelected"
        alternating
        :loading="loading"
        search-field="name"
        :search-value="search"
      >
        <template #loading>
          <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="oklch(0.596 0.145 163.225)"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="M36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
        </template>
        <template #item-description="item">
          <div v-html="item.description"></div>
        </template>
        <template #item-actions="item">
          <div class="flex items-center space-x-3">
            <SpecimensViewDialog :specimen="item" />
            <button
              @click="navigateTo(`/app/specimens/update/${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="fa:edit" class="w-5 h-5" />
            </button>
            <DialogsItemDeleteDialog
              :property="{
                id: item.id,
                name: 'specimen types',
                endpoint: 'specimen_types',
              }"
              @confirm="confirmDeletion"
            >
              <button class="cursor-pointer">
                <Icon icon="mdi:delete" class="w-5 h-5 mr-1" />
              </button>
            </DialogsItemDeleteDialog>
          </div>
        </template>
      </datatable>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "app",
  middleware: ["auth"],
});

useHead({
  title: "Specimens  | Malawi Test Catalog",
});

import type { Header, Item } from "vue3-easy-data-table";
import type { Specimen } from "@/types";
import dayjs from "dayjs";

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const search = ref<string>("");
const dateRange = ref<any>([]);
const itemsSelected = ref<Item[]>([]);
const headers: Header[] = [
  { text: "NAME", value: "name" },
  { text: "OTHER NAMES", value: "preferred_name" },
  { text: "SCIENTIFIC NAME", value: "scientific_name" },
  { text: "MOH CODE", value: "moh_code" },
  { text: "DATE CREATED", value: "created_at" },
  { text: "ACTIONS", value: "actions" },
];
const specimens = ref<Specimen[]>([]);

const filteredSpecimens = computed(() => {
  return specimens.value.map((specimen: Specimen) => {
    return {
      ...specimen,
      created_at: dayjs(specimen.created_at).format(DATE_FORMAT),
    };
  });
});

const exportData = computed(() => ({
  name: `malawi-test-catalog-specimens-${dayjs().format(DATE_FORMAT)}`,
  headers: [
    "Malawi Test Catalog Specimens",
    "Report Date: " + dayjs().format(DATE_FORMAT),
  ],
  data: specimens.value.map((specimen: Specimen) => ({
    NAME: specimen.name,
    "PREFERRED NAME": specimen.preferred_name,
    "SCIENTIFIC NAME": specimen.scientific_name,
    "MOH CODE": specimen.moh_code,
    "CREATED AT": dayjs(specimen.created_at).format(DATE_FORMAT),
  })),
}));

const fetchSpecimens = async (): Promise<void> => {
  loading.value = true;
  try {
    const response: any = await $metadata.specimen_types.getAll();
    if (response) {
      specimens.value = response;
    }
  } catch (error) {
    console.error(error);
    $toast.error("An error occurred while fetching specimen types");
  } finally {
    loading.value = false;
  }
};

const confirmDeletion = (): void => {
  $toast.success("Specimen type deleted successfully");
  fetchSpecimens();
};

onMounted(() => {
  fetchSpecimens();
});
</script>

<style scoped></style>
