<template>
    <div class="min-h-screen bg-gray-50 p-5">
        <div class="mx-auto">
            <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
                <SectionHeader icon="healthicons:microscope-with-specimen-outline" title="Create new specimen"
                    description="Add details of a new specimen type" />
                <div class="w-full py-5 px-5">
                    <BaseForm :config="formConfig" @submit="submitForm">
                        <template v-slot="{ form }">
                            <div class="w-full grid grid-cols-2 gap-5">
                                <BaseInput name="name" label="Name" v-model="specimen.name" required />

                                <BaseInput name="preferred_name" label="Preferred name"
                                    v-model="specimen.preferred_name" required />

                                <BaseInput name="iblis_mapping_name" label="IBLIS mapping name"
                                    v-model="specimen.iblis_mapping_name" />

                                <BaseInput name="scientific_name" label="Scientific name"
                                    v-model="specimen.scientific_name" />

                                <BaseInput name="loinc_code" label="LOINC code" v-model="specimen.loinc_code"/>
                                <BaseInput name="moh_code" label="MOH code" v-model="specimen.moh_code" />
                                <div>
                                    <label for="description"
                                        class="block font-medium text-gray-700 mb-2">Description</label>
                                    <div class="overflow-hidden">
                                        <RichTextEditor theme="snow" class="editor"
                                            v-model:content="specimen.description" contentType="html">
                                        </RichTextEditor>
                                    </div>
                                </div>
                            </div>
                            <BaseButton class="mt-4" label="Submit" :loading="loading" type="submit" />
                        </template>
                    </BaseForm>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Specimen } from '@/types';
import type { FormConfig } from '@/types/input';

definePageMeta({
    layout: "app",
    middleware: ['auth']
});

useHead({
    title: "Create new specimen | Malawi Test Catalog"
});

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const specimen = ref<Specimen>({
    name: "",
    iblis_mapping_name: "",
    description: "",
    id: 0,
    created_at: '',
    updated_at: '',
    moh_code: null,
    nlims_code: null,
    loinc_code: null,
    preferred_name: null,
    scientific_name: null,
});
const formConfig: FormConfig = {
    fields: [
        {
            name: 'name', label: 'Name', rules:
                { required: true }
        },
        {
            name: 'iblis_mapping_name', label: 'IBLIS mapping name', rules:
                { required: false }
        },
        {
            name: 'preferred_name', label: 'Preferred name', rules:
                { required: true }
        },
        {
            name: 'scientific_name', label: 'Scientific name', rules:
                { required: false }
        },
        {
            name: 'loinc_code', label: 'loinc code', rules:
                { required: false }
        },
        {
            name: 'moh_code', label: 'MOH code', rules:
                { required: false }
        }
    ]
}

const submitForm = async (values: any, valid: boolean): Promise<void> => {
    if (valid) {
        loading.value = true;
        try {
            const response = await $metadata.specimen_types.create(specimen.value);
            if (response) {
                $toast.success("Specimen created successfully");
                setTimeout(() => {
                    navigateTo({ path: "/app/specimens" });
                }, 2000);
            }
        } catch (error: any) {
            $toast.error("An error occurred while creating specimen");
        } finally {
            loading.value = false;
        }
    }
}
</script>
