<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="healthicons:microscope-with-specimen-outline"
          title="Edit specimen"
          description="Modify details of the specimen"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="formData.name"
                  required
                />
                <BaseInput
                  name="preferred_name"
                  label="Preferred name"
                  v-model="formData.preferred_name"
                  required
                />

                <BaseInput
                  name="iblis_mapping_name"
                  label="IBLIS mapping name"
                  v-model="formData.iblis_mapping_name"
                />

                <BaseInput
                  name="scientific_name"
                  label="Scientific name"
                  v-model="formData.scientific_name"
                />

                <BaseInput
                  name="loinc_code"
                  label="LONIC code"
                  v-model="formData.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="formData.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="formData.description"
                      contentType="html"
                    ></RichTextEditor>
                  </div>
                </div>
              </div>
              <BaseButton
                :disabled="!form.isValid"
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Specimen } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Update specimen | Malawi Test Catalog",
});

const { $metadata, $toast } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const specimen = ref<Specimen | null>(null);
const formData = reactive({
  name: "",
  description: "",
  iblis_mapping_name: "",
  preferred_name: "",
  scientific_name: "",
  loinc_code: "",
  moh_code: "",
  nlims_code: "",
  id: 0,
  created_at: "",
  updated_at: "",
});

const formConfig: FormConfig = {
  fields: [
    { name: "name", label: "Name", rules: { required: true } },
    {
      name: "iblis_mapping_name",
      label: "IBLIS mapping name",
      rules: { required: false },
    },
    {
      name: "preferred_name",
      label: "Preferred name",
      rules: { required: true },
    },
    {
      name: "scientific_name",
      label: "Scientific name",
      rules: { required: false },
    },
    { name: "loinc_code", label: "LONIC code", rules: { required: false } },
    { name: "moh_code", label: "MOH code", rules: { required: false } },
  ],
};

const fetchSpecimen = async (): Promise<void> => {
  const id = route.params.id;
  if (!id) {
    $toast.error("Invalid specimen ID");
    setTimeout(() => {
      router.push("/app/specimens");
    }, 2000);
    return;
  }

  loading.value = true;
  try {
    const response: any = await $metadata.specimen_types.getById(Number(id));
    specimen.value = response;

    if (specimen.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          specimen.value &&
          key in specimen.value &&
          specimen.value[key as keyof Specimen] !== undefined
        ) {
          (formData as any)[key] = specimen.value[key as keyof Specimen];
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch specimen data");
    setTimeout(() => {
      router.push("/app/specimens");
    }, 2000);
  } finally {
    loading.value = false;
  }
};

onMounted(fetchSpecimen);

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (!valid || !specimen.value) return;
  loading.value = true;
  try {
    const updatedspecimen = {
      ...specimen.value,
      ...formData,
    };
    await $metadata.specimen_types.update(updatedspecimen.id, updatedspecimen);
    $toast.success("specimen updated successfully");
    setTimeout(() => {
      router.push("/app/specimens");
    }, 2000);
  } catch (error) {
    $toast.error("An error occurred while updating specimen");
  } finally {
    loading.value = false;
  }
};
</script>
