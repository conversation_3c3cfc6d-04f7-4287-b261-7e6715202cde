<template>
  <div class="px-4 py-8">
    <div class="">
      <h1 class="text-3xl font-semibold mb-6">Departments</h1>

      <div
        class="w-full flex items-center bg-white p-2 border border-gray-100 rounded my-2 px-2"
      >
        <!-- <div class="w-full flex items-center space-x-3">
          <div class="flex items-center pr-2">
            <p><span class="text-lg font-medium">Date created:</span></p>
            <div class="ml-2">
              <Datepicker
                required
                position="left"
                placeholder="select start & end date"
                :range="true"
                input-class-name="datepicker"
                v-model="dateRange"
                :format="DATE_PICKER"
                :maxDate="new Date()"
              />
            </div>
          </div>
        </div> -->
      </div>

      <div class="w-full flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="$router.push('/app/departments/create')"
            class="cursor-pointer bg-emerald-600 hover:bg-emerald-700 transition duration-150 flex items-center text-emerald-100 px-2.5 py-2 rounded"
          >
            <Icon icon="mdi:plus" class="w-5 h-5 mr-1" />
            Add new
          </button>
          <BaseExcelExport :export-data="exportData" />
        </div>
        <div class="max-w-md w-full">
          <BaseSearchbar v-model="search" clearable placeholder="Search..." />
        </div>
      </div>
    </div>
    <div class="mt-5">
      <datatable
        table-class-name="datatable"
        :headers="headers"
        :items="filteredDepartments"
        v-model:items-selected="itemsSelected"
        alternating
        :loading="loading"
        search-field="name"
        :search-value="search"
      >
        <template #loading>
          <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="oklch(0.596 0.145 163.225)"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="M36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
        </template>
        <template #item-description="item">
          <div v-html="item.description"></div>
        </template>
        <template #item-actions="item">
          <div class="flex items-center space-x-3">
            <DepartmentsViewDialog :department="item" />
            <button
              @click="navigateTo(`/app/departments/update/${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="fa:edit" class="w-5 h-5" />
            </button>
            <DialogsItemDeleteDialog
              :property="{
                id: item.id,
                name: 'departments',
                endpoint: 'departments',
              }"
              @confirm="confirmDeletion"
            >
              <button class="cursor-pointer">
                <Icon icon="mdi:delete" class="w-5 h-5 mr-1" />
              </button>
            </DialogsItemDeleteDialog>
          </div>
        </template>
      </datatable>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "app",
  middleware: ["auth"],
});

useHead({
  title: "Departments  | Malawi Test Catalog",
});

import type { Header, Item } from "vue3-easy-data-table";
import type { Department } from "@/types";
import dayjs from "dayjs";

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const search = ref<string>("");
const itemsSelected = ref<Item[]>([]);
const dateRange = ref<Date[]>([]);
const headers: Header[] = [
  { text: "NAME", value: "name" },
  { text: "SHORT NAME", value: "short_name" },
  { text: "OTHER NAMES", value: "preferred_name" },
  { text: "DESCRIPTION", value: "description" },
  { text: "MOH CODE", value: "moh_code" },
  { text: "DATE CREATED", value: "created_at" },
  { text: "ACTIONS", value: "actions" },
];
const departments = ref<Department[]>([]);

const filteredDepartments = computed(() =>
  departments.value.map((department: Department) => ({
    ...department,
    created_at: dayjs(department.created_at).format(DATE_FORMAT),
  }))
);

const exportData = computed(() => ({
  name: `malawi-test-catalog-departments-${dayjs().format(DATE_FORMAT)}`,
  headers: [
    "Malawi Test Catalog Departments",
    "Report Date: " + dayjs().format(DATE_FORMAT),
  ],
  data: departments.value.map((department: Department) => ({
    NAME: department.name,
    "PREFERRED NAME": department.preferred_name,
    "SCIENTIFIC NAME": department.scientific_name,
    DESCRIPTION: department.description,
    "MOH CODE": department.moh_code,
    "CREATED AT": dayjs(department.created_at).format(DATE_FORMAT),
  })),
}));

const fetchdepartments = async (): Promise<void> => {
  loading.value = true;
  try {
    const response: any = await $metadata.departments.getAll();
    if (response) {
      departments.value = response;
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const confirmDeletion = () => {
  $toast.success("Department deleted successfully!");
  fetchdepartments();
};

onMounted(() => {
  fetchdepartments();
});
</script>

<style scoped></style>
