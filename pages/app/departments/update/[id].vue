<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="circum:hospital-1"
          title="Update Department"
          description="Modify details of the department"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput name="name" label="Name" v-model="formData.name" />
                <BaseInput
                  name="short_name"
                  label="Short name"
                  v-model="formData.short_name"
                  required
                />
                <BaseInput
                  name="preferred_name"
                  label="Preferred name"
                  v-model="formData.preferred_name"
                  required
                />
                <BaseInput
                  name="scientific_name"
                  label="Scientific name"
                  v-model="formData.scientific_name"
                />
                <BaseInput
                  name="loinc_code"
                  label="LONIC code"
                  v-model="formData.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="formData.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="formData.description"
                      contentType="html"
                    ></RichTextEditor>
                  </div>
                </div>
              </div>
              <BaseButton
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Department } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Update Department | Malawi Test Catalog",
  meta: [{ name: "description", content: "Update department details" }],
});

const { $metadata, $toast } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const department = ref<Department | null>(null);
const formData = reactive({
  name: "",
  short_name: "",
  description: "",
  preferred_name: "",
  scientific_name: "",
  loinc_code: "",
  moh_code: "",
  nlims_code: "",
  id: 0,
  created_at: "",
  updated_at: "",
});

const formConfig: FormConfig = {
  fields: [
    { name: "name", label: "Name", rules: { required: true } },
    { name: "short_name", label: "Short name", rules: { required: false } },
    {
      name: "preferred_name",
      label: "Preferred name",
      rules: { required: true },
    },
    {
      name: "scientific_name",
      label: "Scientific name",
      rules: { required: false },
    },
    { name: "loinc_code", label: "LONIC code", rules: { required: false } },
    { name: "moh_code", label: "MOH code", rules: { required: false } },
  ],
};

const fetchDepartments = async () => {
  const id = route.params.id;
  if (!id) {
    $toast.error("Invalid department ID");
    setTimeout(() => {
      router.push("/app/departments");
    }, 2000);
    return;
  }

  loading.value = true;
  try {
    const response: any = await $metadata.departments.getById(Number(id));
    department.value = response;

    if (department.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          department.value &&
          key in department.value &&
          department.value[key as keyof Department] !== undefined
        ) {
          (formData as any)[key] = department.value[key as keyof Department];
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch department data");
    setTimeout(() => {
      router.push("/app/departments");
    }, 2000);
  } finally {
    loading.value = false;
  }
};

onMounted(fetchDepartments);

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (!valid || !department.value) return;
  loading.value = true;
  try {
    const updatedepartment = {
      ...department.value,
      ...formData,
    };
    const response: any = await $metadata.departments.update(
      updatedepartment.id,
      updatedepartment
    );
    if (response) {
      $toast.success("Department updated successfully");
      setTimeout(() => {
        router.push("/app/departments");
      }, 2000);
    }
  } catch (error) {
    $toast.error("An error occurred while updating department");
  } finally {
    loading.value = false;
  }
};
</script>
