<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="eos-icons:products-outlined"
          title="Update Product"
          description="Modify details of the product"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="formData.name"
                  required
                />
                <BaseInput
                  name="loinc_code"
                  label="LONIC code"
                  v-model="formData.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="formData.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="formData.description"
                      contentType="html"
                    ></RichTextEditor>
                  </div>
                </div>
              </div>
              <BaseButton
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Product } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Update Product | Malawi Test Catalog",
  meta: [{ name: "description", content: "Update product details" }],
});

const { $metadata, $toast } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const product = ref<Product | null>(null);
const formData = reactive({
  name: "",
  description: "",
  loinc_code: "",
  moh_code: "",
  nlims_code: "",
  id: 0,
  created_at: "",
  updated_at: "",
});

const formConfig: FormConfig = {
  fields: [
    { name: "name", label: "Name", rules: { required: true } },
    { name: "loinc_code", label: "LONIC code", rules: { required: false } },
    { name: "moh_code", label: "MOH code", rules: { required: false } },
  ],
};

const fetchProduct = async (): Promise<void> => {
  const id = route.params.id;
  if (!id) {
    $toast.error("Invalid product ID");
    setTimeout(() => {
      router.push("/app/products");
    }, 3000);
    return;
  }

  loading.value = true;

  try {
    const response: any = await $metadata.products.getById(Number(id));
    product.value = response;

    if (product.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          product.value &&
          key in product.value &&
          product.value[key as keyof Product] !== undefined
        ) {
          (formData as any)[key] = product.value[key as keyof Product];
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch product data");
    setTimeout(() => {
      router.push("/app/products");
    }, 3000);
  } finally {
    loading.value = false;
  }
};

onMounted(fetchProduct);

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (!valid || !product.value) return;

  loading.value = true;
  try {
    const updateproduct = {
      ...product.value,
      ...formData,
    };
    await $metadata.products.update(updateproduct.id, updateproduct);
    $toast.success("Product updated successfully");
    setTimeout(() => {
      router.push("/app/products");
    }, 3000);
  } catch (error) {
    $toast.error("An error occurred while updating product");
  } finally {
    loading.value = false;
  }
};
</script>
