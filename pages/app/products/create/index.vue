<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="eos-icons:products-outlined"
          title="Create product"
          description="Add details of the new product"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="product.name"
                  required
                />
                <BaseInput
                  name="lonic_code"
                  label="LONIC code"
                  v-model="product.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="product.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="product.description"
                      contentType="html"
                    >
                    </RichTextEditor>
                  </div>
                </div>
              </div>
              <BaseButton
                class="mt-4"
                label="Submit"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Product } from "@/types";
import type { FormConfig } from "@/types/input";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Create product | Malawi Test Catalog",
  meta: [{ name: "description", content: "Create product details" }],
});

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const product = ref<Product>({
  name: "",
  description: "",
  id: 0,
  created_at: "",
  updated_at: "",
  moh_code: null,
  nlims_code: null,
  loinc_code: null,
});

const formConfig: FormConfig = {
  fields: [
    {
      name: "name",
      label: "Name",
      rules: { required: true },
    },
    {
      name: "lonic_code",
      label: "LONIC code",
      rules: { required: false },
    },
    {
      name: "moh_code",
      label: "MOH code",
      rules: { required: false },
    },
  ],
};

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (valid) {
    loading.value = true;
    try {
      const response = await $metadata.products.create(product.value);
      if (response) {
        $toast.success("product created successfully");
        navigateTo({ path: "/app/products" });
      }
    } catch (error: any) {
      $toast.error("An error occurred while creating product");
    } finally {
      loading.value = false;
    }
  }
};
</script>
