<template>
  <div class="px-4 py-8">
    <div>
      <h1 class="text-3xl font-semibold py-4">Test Types </h1>
    </div>
    <div
      class="w-full flex flex-col sm:flex-row items-start sm:items-center justify-between bg-white border border-gray-100 rounded my-2 px-2 py-2"
    >
      <div class="w-full flex flex-col lg:flex-row items-start lg:items-center lg:space-x-6 space-y-3 lg:space-y-0">
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
          <p class="whitespace-nowrap">
            <span class="text-lg font-medium">Test category:</span>
          </p>
          <div class="w-full sm:w-64">
            <Combobox v-model="selectedDepartment">
            <div class="relative mt-1 z-50">
              <div
                class="relative w-full cursor-default overflow-hidden rounded bg-gray-50 text-leftfocus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-emerald-300"
              >
                <ComboboxInput
                  class="w-full border-none py-2 pl-3 leading-5 text-gray-900 focus:ring-0"
                  :displayValue="(department: any) => department.name"
                  @change="query = $event.target.value"
                />
                <ComboboxButton
                  class="absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <ChevronUpDownIcon
                    class="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </ComboboxButton>
              </div>
              <TransitionRoot
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
                @after-leave="query = ''"
              >
                <ComboboxOptions
                  class="absolute mt-1 max-h-60 w-full overflow-auto rounded bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none"
                >
                  <div
                    v-if="departments.length === 0 && query !== ''"
                    class="relative cursor-default select-none px-4 py-2 text-gray-700"
                  >
                    Nothing found.
                  </div>

                  <ComboboxOption
                    v-for="department in departments"
                    as="template"
                    :key="department.id"
                    :value="department"
                    v-slot="{ selected, active }"
                  >
                    <li
                      class="relative cursor-default select-none py-2 pl-10 pr-4"
                      :class="{
                        'bg-emerald-600 text-white': active,
                        'text-gray-900': !active,
                      }"
                    >
                      <span
                        class="block truncate"
                        :class="{
                          'font-medium': selected,
                          'font-normal': !selected,
                        }"
                      >
                        {{ department.name }}
                      </span>
                      <span
                        v-if="selected"
                        class="absolute inset-y-0 left-0 flex items-center pl-3"
                        :class="{
                          'text-white': active,
                          'text-emerald-600': !active,
                        }"
                      >
                        <CheckIcon class="h-5 w-5" aria-hidden="true" />
                      </span>
                    </li>
                  </ComboboxOption>
                </ComboboxOptions>
              </TransitionRoot>
            </div>
          </Combobox>
          </div>
        </div>
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
          <p class="whitespace-nowrap"><span class="text-lg font-medium">Specimen:</span></p>
          <div class="w-full sm:w-64">
            <Combobox v-model="selected">
            <div class="relative mt-1 z-50">
              <div
                class="relative w-full cursor-default overflow-hidden rounded bg-gray-50 text-leftfocus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-emerald-300"
              >
                <ComboboxInput
                  class="w-full border-none py-2 pl-3 pr-10 leading-5 text-gray-900 focus:ring-0"
                  :displayValue="(specimen: any) => specimen.name"
                  @change="query = $event.target.value"
                />
                <ComboboxButton
                  class="absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <ChevronUpDownIcon
                    class="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </ComboboxButton>
              </div>
              <TransitionRoot
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
                @after-leave="query = ''"
              >
                <ComboboxOptions
                  class="absolute mt-1 max-h-60 w-full overflow-auto rounded bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none"
                >
                  <div
                    v-if="filteredSpecimens.length === 0 && query !== ''"
                    class="relative cursor-default select-none px-4 py-2 text-gray-700"
                  >
                    Nothing found.
                  </div>

                  <ComboboxOption
                    v-for="specimen in filteredSpecimens"
                    as="template"
                    :key="specimen.id"
                    :value="specimen"
                    v-slot="{ selected, active }"
                  >
                    <li
                      class="relative cursor-default select-none py-2 pl-10 pr-4"
                      :class="{
                        'bg-emerald-600 text-white': active,
                        'text-gray-900': !active,
                      }"
                    >
                      <span
                        class="block truncate"
                        :class="{
                          'font-medium': selected,
                          'font-normal': !selected,
                        }"
                      >
                        {{ specimen.name }}
                      </span>
                      <span
                        v-if="selected"
                        class="absolute inset-y-0 left-0 flex items-center pl-3"
                        :class="{
                          'text-white': active,
                          'text-emerald-600': !active,
                        }"
                      >
                        <CheckIcon class="h-5 w-5" aria-hidden="true" />
                      </span>
                    </li>
                  </ComboboxOption>
                </ComboboxOptions>
              </TransitionRoot>
            </div>
          </Combobox>
          </div>
        </div>
      </div>
      <div class="py-2 sm:py-0">
        <Popover class="relative">
          <PopoverButton
            class="bg-gray-100 cursor-pointer hover:bg-emerald-100 hover:text-emerald-600 hover:font-medium transition duration-150 rounded-full flex items-center px-2 py-1.5 my-2 w-full sm:w-auto justify-center sm:justify-start outline-none relative"
            :class="{
              'bg-emerald-100 text-emerald-600 font-medium': hasActiveAdvancedFilters
            }"
          >
            <div
              class="w-6 h-6 flex items-center self-center mr-2 rounded-full justify-center bg-emerald-700 text-emerald-100"
              :class="{
                'bg-emerald-600': hasActiveAdvancedFilters
              }"
            >
              <Icon icon="fluent-mdl2:filters" class="w-4 h-4" />
            </div>
            Filters
            <div
              v-if="hasActiveAdvancedFilters"
              class="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white"
            ></div>
          </PopoverButton>

          <transition
            enter-active-class="transition duration-200 ease-out"
            enter-from-class="translate-y-1 opacity-0"
            enter-to-class="translate-y-0 opacity-100"
            leave-active-class="transition duration-150 ease-in"
            leave-from-class="translate-y-0 opacity-100"
            leave-to-class="translate-y-1 opacity-0"
          >
            <PopoverPanel
              class="absolute right-0 top-full mt-2 w-80 bg-white rounded shadow-lg ring-1 ring-gray-200 ring-opacity-5 z-50"
            >
              <div class="p-4 space-y-4">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">Advanced Filters</h3>
                  <button
                    @click="clearAllFilters"
                    class="text-sm text-gray-500 cursor-pointer hover:text-gray-700"
                  >
                    Clear all
                  </button>
                </div>

                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">Gender</label>
                  <select
                    v-model="advancedFilters.sex"
                    class="w-full border border-gray-300 rounded px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">All</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Both">Both</option>
                  </select>
                </div>

                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">Target TAT</label>
                  <select
                    v-model="advancedFilters.target_tat"
                    class="w-full border border-gray-300 rounded px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">All</option>
                    <option value="30 Minutes">30 Minutes</option>
                    <option value="1 hour">1 Hour</option>
                    <option value="2 Hours">2 Hours</option>
                    <option value="24 Hours">24 Hours</option>
                    <option value="7 Days">7 Days</option>
                  </select>
                </div>

                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">Has NLIMS Code</label>
                  <select
                    v-model="advancedFilters.has_nlims_code"
                    class="w-full border border-gray-300 rounded px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">All</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>

                <div class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">Has LOINC Code</label>
                  <select
                    v-model="advancedFilters.has_loinc_code"
                    class="w-full border border-gray-300 rounded px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">All</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>

                <div class="flex items-center gap-2 pt-4">
                  <button
                    @click="applyAdvancedFilters"
                    class="flex-1 cursor-pointer bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded transition duration-150 font-medium"
                  >
                    Apply Filters
                  </button>
                  <button
                    @click="resetAdvancedFilters"
                    class="px-4 cursor-pointer py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 transition duration-150"
                  >
                    Reset
                  </button>
                </div>
              </div>
            </PopoverPanel>
          </transition>
        </Popover>
      </div>
    </div>
    <div class="w-full flex flex-col md:flex-row items-start md:items-center justify-between gap-4 my-4">
      <div class="flex items-center space-x-4 w-full md:w-auto">
        <button
          @click="$router.push('/app/test-types/create')"
          class="cursor-pointer bg-emerald-600 hover:bg-emerald-700 transition duration-150 flex items-center text-emerald-100 px-2.5 py-2 rounded w-full md:w-auto"
        >
          <Icon icon="mdi:plus" class="w-5 h-5 mr-1" />
          Add new
        </button>
        <BaseExcelExport :export-data="exportData" />
        <DialogsImportDialog
          endpoint="test_types/import"
          title="Import Test Types"
          description="Upload an Excel file containing test type data. The system will process and import valid entries."
          success-message="Successfully imported test types from {fileName}"
          @import-success="fetchTestTypes"
        />
      </div>
      <BaseSearchbar v-model="search" placeholder="Search test types" class="w-full md:w-auto" />
    </div>
    <div class="mt-5">
      <datatable
        table-class-name="datatable"
        :headers="headers"
        :items="testTypes"
        v-model:items-selected="itemsSelected"
        v-model:server-options="serverOptions"
        :server-items-length="totalItems"
        alternating
        :loading="loading"
        server-side
      >
        <template #loading>
          <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="oklch(0.596 0.145 163.225)"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="M36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
        </template>
        <template #item-name="item">
          <p class="font-medium text-lg">{{ item.name }}</p>
        </template>
        <template #item-description="item">
          <div class="line-clamp-2" v-html="item.description"></div>
        </template>
        <template #item-actions="item">
          <div class="flex items-center space-x-2">
            <button
              @click="viewTestType(item.id)"
              class="cursor-pointer"
            >
              <Icon icon="lets-icons:view-alt-duotone" class="w-5 h-5 mr-1" />
            </button>
            <button
              @click="$router.push(`/app/test-types/update?id=${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="fa:edit" class="w-5 h-5 mr-1" />
            </button>
            <DialogsItemDeleteDialog
              :property="{
                id: item.id,
                name: 'test type',
                endpoint: 'test_types',
              }"
              @confirm="confirmDeletion"
            >
              <button class="cursor-pointer">
                <Icon icon="mdi:delete" class="w-5 h-5 mr-1" />
              </button>
            </DialogsItemDeleteDialog>
          </div>
        </template>
      </datatable>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "app",
  middleware: ['auth'],
  keepalive: false
});

useHead({
  title: "Test Types | Malawi Test Catalog",
});

import type { Header, Item } from "vue3-easy-data-table";
import type { Department, Specimen, TestType } from "@/types";
import type { PaginatedApiResponse } from "@/types/api";
import dayjs from "dayjs";
import {
  Combobox,
  ComboboxInput,
  ComboboxButton,
  ComboboxOptions,
  ComboboxOption,
  TransitionRoot,
  Popover,
  PopoverButton,
  PopoverPanel,
} from "@headlessui/vue";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/vue/20/solid";
import DialogsImportDialog from "~/components/dialogs/ImportDialog.vue";

const DATE_FORMAT = "YYYY-MM-DD";

const { $api, $toast, $metadata } = useNuxtApp();
const search = ref<string>("");
const loading = ref<boolean>(false);
const itemsSelected = ref<Item[]>([]);
const totalItems = ref<number>(0);
const serverOptions = ref({
  page: 1,
  rowsPerPage: 25,
  sortBy: '',
  sortType: 'asc'
});

const headers: Header[] = [
  { text: "NAME", value: "name", sortable: true },
  { text: "SHORT NAME", value: "short_name", sortable: true },
  { text: "OTHER NAMES", value: "preferred_name", sortable: true },
  { text: "NLIMS CODE", value: "nlims_code", sortable: true },
  { text: "DESCRIPTION", value: "description", sortable: false },
  { text: "DATE CREATED", value: "created_at", sortable: true },
  { text: "ACTIONS", value: "actions", sortable: false },
];
const testTypes = ref<TestType[]>([]);
const specimens = ref<Specimen[]>([]);
const departments = ref<Department[]>([]);
const selectedDepartment = ref();

let selected = ref(specimens.value[0]);
let query = ref<string>("");

const advancedFilters = ref({
  sex: '',
  target_tat: '',
  has_nlims_code: '',
  has_loinc_code: ''
});

const hasActiveAdvancedFilters = computed(() => {
  return Object.values(advancedFilters.value).some(value => value !== '');
});

const filteredSpecimens = computed(() => {
  return specimens.value.filter((specimen: Specimen) => {
    return specimen.name.toLowerCase().includes(query.value.toLowerCase());
  });
});

const buildApiParams = () => {
  const params: any = {
    page: serverOptions.value.page,
    per_page: serverOptions.value.rowsPerPage
  };

  if (search.value) {
    params.search = search.value;
  }

  if (selectedDepartment.value && selectedDepartment.value.id !== 0) {
    params.test_category_id = selectedDepartment.value.id;
  }

  if (selected.value && selected.value.id !== 0) {
    params.specimen_type_id = selected.value.id;
  }

  if (advancedFilters.value.sex) {
    params.sex = advancedFilters.value.sex;
  }

  if (advancedFilters.value.target_tat) {
    params.target_tat = advancedFilters.value.target_tat;
  }

  if (advancedFilters.value.has_nlims_code) {
    params.has_nlims_code = advancedFilters.value.has_nlims_code;
  }

  if (advancedFilters.value.has_loinc_code) {
    params.has_loinc_code = advancedFilters.value.has_loinc_code;
  }

  if (serverOptions.value.sortBy) {
    params.sort_by = serverOptions.value.sortBy;
    params.sort_order = serverOptions.value.sortType;
  }

  return params;
};

const exportData = computed(() => ({
  name: `malawi-test-catalog-test-types-${dayjs().format(DATE_FORMAT)}`,
  headers: [
    "Malawi Test Catalog Test Types",
    "Report Date: " + dayjs().format(DATE_FORMAT),
  ],
  data: allTestTypesForExport.value.map((testType: TestType) => {
    return {
      NAME: testType.name,
      "SHORT NAME": testType.short_name,
      "PREFERRED NAME": testType.preferred_name,
      DESCRIPTION: testType.description,
      "TARGET TURN AROUND TIME": testType.targetTAT,
      "MOH CODE": testType.moh_code,
      "NLIMS CODE": testType.nlims_code,
      "LONIC CODE": testType.loinc_code,
      "CAN BE DONE ON SEX": testType.can_be_done_on_sex,
      "DATE CREATED": dayjs(testType.created_at).format(DATE_FORMAT),
    };
  }),
}));

const allTestTypesForExport = ref<TestType[]>([]);

const fetchAllTestTypesForExport = async (): Promise<void> => {
  const params = {
    ...buildApiParams(),
    no_paginate: true
  };
  delete params.page;
  delete params.per_page;

  try {
    const response = await $api.get<TestType[]>("test_types", { params });
    allTestTypesForExport.value = response.map((testType: TestType) => ({
      ...testType,
      created_at: dayjs(testType.created_at).format(DATE_FORMAT),
    }));
  } catch (error) {
    console.error("error occurred while fetching export data: ", error);
    $toast.error("An error occurred while preparing export data");
  }
};

const fetchTestTypes = async (): Promise<void> => {
  loading.value = true;
  const params = buildApiParams();

  try {
    const response = await $api.get<PaginatedApiResponse<TestType> | TestType[]>("test_types", { params });

    if (Array.isArray(response)) {
      testTypes.value = response.map((testType: TestType) => ({
        ...testType,
        created_at: dayjs(testType.created_at).format(DATE_FORMAT),
      }));
      totalItems.value = response.length;
    } else {
      testTypes.value = response.data.map((testType: TestType) => ({
        ...testType,
        created_at: dayjs(testType.created_at).format(DATE_FORMAT),
      }));
      totalItems.value = response.pagination.total_count;
    }
  } catch (error) {
    console.error("error occurred: ", error);
    $toast.error("An error occurred while fetching test types");
  } finally {
    loading.value = false;
  }
};

const confirmDeletion = (): void => {
  $toast.success("Test type deleted successfully");
  fetchTestTypes();
};

const applyAdvancedFilters = (): void => {
  serverOptions.value.page = 1;
  fetchTestTypes();
  fetchAllTestTypesForExport();
};

const resetAdvancedFilters = (): void => {
  advancedFilters.value = {
    sex: '',
    target_tat: '',
    has_nlims_code: '',
    has_loinc_code: ''
  };
  serverOptions.value.page = 1;
  fetchTestTypes();
  fetchAllTestTypesForExport();
};

const clearAllFilters = (): void => {
  selectedDepartment.value = departments.value[0];
  selected.value = specimens.value[0];
  search.value = '';

  resetAdvancedFilters();
};

// Improved navigation function to view test type details
const viewTestType = (id: number): void => {
  // Force clean navigation to view page
  router.push({
    path: `/app/test-types/view`,
    query: { id: id.toString() }
  });
};

watch(serverOptions, () => {
  fetchTestTypes();
}, { deep: true });

const debouncedSearch = ref('');
let searchTimeout: NodeJS.Timeout;

watch(search, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    debouncedSearch.value = newValue;
    serverOptions.value.page = 1;
    fetchTestTypes();
  }, 500);
});

watch([selectedDepartment, selected], () => {
  serverOptions.value.page = 1;
  fetchTestTypes();
});

watch([selectedDepartment, selected, search, advancedFilters], () => {
  fetchAllTestTypesForExport();
}, { deep: true });

// Add activated hook to handle page revisits when using Vue Router
const router = useRouter();
onActivated(() => {
  // Refresh data when the page is revisited via navigation
  fetchTestTypes();
});

const setupDepartment = async (): Promise<void> => {
  const data: any = await $metadata.departments.getAll();
  departments.value = data;
  departments.value.unshift({
    id: 0,
    name: "All",
    description: "",
    created_at: "",
    updated_at: "",
    moh_code: null,
    nlims_code: null,
    loinc_code: null,
    preferred_name: null,
    scientific_name: null,
    short_name: null,
  });
  selectedDepartment.value = departments.value[0];
};

onMounted(async () => {
  // Reset the page state when entering this page
  loading.value = true;
  testTypes.value = [];

  // Force data reload
  await fetchTestTypes();
  fetchAllTestTypesForExport();
  await setupDepartment();

  const data: any = await $metadata.specimen_types.getAll();
  specimens.value = data;
  specimens.value.unshift({
    id: 0,
    name: "All",
    description: "",
    created_at: "",
    updated_at: "",
    moh_code: null,
    nlims_code: null,
    loinc_code: null,
    preferred_name: null,
    scientific_name: null,
    iblis_mapping_name: null,
  });
  selected.value = specimens.value[0];
});
</script>

<style scoped>
.datatable {
  --easy-table-header-font-size: 17px;
  --easy-table-body-row-font-size: 15px;
  --easy-table-footer-font-size: 15px;
}
</style>
