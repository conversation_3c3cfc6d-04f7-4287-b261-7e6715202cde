<template>
    <div class="min-h-screen bg-gray-50 p-5">
        <div class="mx-auto">
            <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
                <SectionHeader icon="healthicons:test-tubes-outline" title="Create Test Type"
                    description="Add a new test type with associated measures and specifications" />
                <BaseStepper
                    :steps="steps"
                    :initial-step="currentStep"
                    @validation-errors="getValidationErrors"
                    :step-statuses="stepStatuses"
                    @step-changed="handleStepChange"
                    @submit="createTestType"
                    :data="testCatalog"
                    :submitting="loading">
                    <template #default="{ currentStep }">
                        <component :is="getStepComponent(currentStep).component"
                            v-bind="getStepComponent(currentStep).props" :ref="`stepper-current-${currentStep}`"
                            @update:selectedSpecimens="handleSpecimenUpdate"
                            @update:selectedOrganisms="handleOrganismsUpdate" />
                    </template>
                </BaseStepper>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTestType } from "@/composables/useTestType";
import type { Step } from "~/types/form";

definePageMeta({
    layout: "app",
    middleware: ['auth']
});

useHead({
    title: "Create Test Type | Malawi Test Catalog",
});

const router = useRouter();
const { $api, $toast } = useNuxtApp();

const {
    currentStep,
    loading,
    testCatalog,
    stepStatuses,
    validationErrors,
    handleStepChange,
    getValidationErrors,
    handleSpecimenUpdate,
    handleOrganismsUpdate,
    getSteps,
    prepareDataForSubmission,
} = useTestType();

const steps = <Step[]>getSteps();

const getStepComponent = (currentStep: number) => {
    const step = steps.find((step) => step.id === currentStep);
    if (step) {
        return step;
    }
    return { component: null, props: {} };
};

const createTestType = async (): Promise<void> => {
    loading.value = true;

    await $api
        .post("test_types", {
            test_catalog: prepareDataForSubmission(),
        })
        .then((response: any) => {
            if (response) {
                $toast.success("Test type created successfully");
                setTimeout(() => {
                    router.push("/app/test-types");
                }, 2000);
            }
        })
        .catch((error: any) => {
            $toast.error("Error creating test type");
            console.error("Error creating test type:", error);
        })
        .finally(() => {
            loading.value = false;
        });
};
</script>

<style scoped>
.editor {
    min-height: 200px;
    overflow-y: auto;
}
</style>
