<template>
  <div class="view-test-type">
    <!-- Loading overlay -->
    <div
      v-if="loading"
      class="fixed inset-0 flex items-center justify-center bg-black/25 z-50"
    >
      <div
        class="bg-white px-10 py-5 flex flex-col items-center space-y-2 rounded shadow-lg"
      >
        <svg
          class="w-10 h-10 text-emerald-600"
          width="38"
          height="38"
          viewBox="0 0 38 38"
          xmlns="http://www.w3.org/2000/svg"
          stroke="oklch(0.596 0.145 163.225)"
        >
          <g fill="none" fill-rule="evenodd">
            <g transform="translate(1 1)" stroke-width="2">
              <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
              <path d="M36 18c0-9.94-8.06-18-18-18">
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  from="0 18 18"
                  to="360 18 18"
                  dur="1s"
                  repeatCount="indefinite"
                />
              </path>
            </g>
          </g>
        </svg>
        <span class="text-lg font-semibold">Loading, please wait...</span>
      </div>
    </div>

    <div class="min-h-screen bg-gray-50 p-5">
      <div class="mx-auto">
        <div
          v-if="!loading && !hasData"
          class="bg-white p-5 rounded shadow text-center"
        >
          <svg
            class="w-16 h-16 mx-auto text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <h2 class="text-xl font-semibold mt-4">
            No test catalog data available
          </h2>
          <p class="text-gray-500 mt-2">
            The requested test type could not be found.
          </p>
        </div>

        <template v-if="!loading && hasData">
          <div
            class="bg-white rounded-t border border-gray-100 overflow-hidden"
          >
            <SectionHeader
              icon="healthicons:test-tubes-outline"
              :title="testCatalog?.name || 'Test Type'"
              description="View test type with associated measures and specifications"
            />
          </div>

          <div class="bg-white p-5 rounded mb-6 shadow-sm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div class="mb-4">
                  <h3 class="text-lg font-medium">Basic Information</h3>
                  <div
                    class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
                  >
                    <div class="grid grid-cols-1 gap-2">
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Short Name:</span
                        >
                        <span>{{ testCatalog?.short_name || "N/A" }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Preferred Name:</span
                        >
                        <span>{{ testCatalog?.preferred_name || "N/A" }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Scientific Name:</span
                        >
                        <span>{{ testCatalog?.scientific_name || "N/A" }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Test Category:</span
                        >
                        <span>{{
                          testCatalog.test_category?.name || "N/A"
                        }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Target TAT:</span
                        >
                        <span>{{ testCatalog?.targetTAT || "N/A" }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Can Be Done On Sex:</span
                        >
                        <span>{{
                          testCatalog?.can_be_done_on_sex || "N/A"
                        }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Assay Format:</span
                        >
                        <span>{{ testCatalog?.assay_format || "N/A" }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <h3 class="text-lg font-medium">Codes</h3>
                  <div
                    class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
                  >
                    <div class="grid grid-cols-1 gap-2">
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >LOINC Code:</span
                        >
                        <span>{{ testCatalog?.loinc_code || "N/A" }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >MOH Code:</span
                        >
                        <span>{{ testCatalog?.moh_code || "N/A" }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >NLIMS Code:</span
                        >
                        <span>{{ testCatalog?.nlims_code || "N/A" }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <div class="mb-4">
                  <h3 class="text-lg font-medium">Requirements</h3>
                  <div
                    class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
                  >
                    <div class="grid grid-cols-1 gap-2">
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >HR Cadre:</span
                        >
                        <span>{{
                          testCatalog?.hr_cadre_required || "N/A"
                        }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="font-medium text-emerald-900"
                          >Prevalence Threshold:</span
                        >
                        <span>{{
                          testCatalog?.prevalence_threshold || "N/A"
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <h3 class="text-lg font-medium">Specimen Types</h3>
                  <div
                    class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
                  >
                    <div class="grid grid-cols-1 gap-2">
                      <div>
                        <div class="mt-1">
                          <span
                            v-if="
                              !testCatalog.specimen_types ||
                              testCatalog.specimen_types.length === 0
                            "
                            >N/A</span
                          >
                          <ul v-else class="list-disc list-inside">
                            <li
                              v-for="specimen in testCatalog.specimen_types"
                              :key="specimen.id"
                            >
                              {{ specimen.name }}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <h3 class="text-lg font-medium">Organisms</h3>
                  <div
                    class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
                  >
                    <div class="grid grid-cols-1 gap-2">
                      <div>
                        <div class="mt-1">
                          <span
                            v-if="
                              !testCatalog.organisms ||
                              testCatalog.organisms.length === 0
                            "
                            >N/A</span
                          >
                          <ul v-else class="list-disc list-inside">
                            <li
                              v-for="organism in testCatalog.organisms"
                              :key="organism.id"
                            >
                              {{ organism.name }}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <h3 class="text-lg font-medium">Equipment & Sites</h3>
                  <div
                    class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
                  >
                    <div class="grid grid-cols-1 gap-2">
                      <div>
                        <span class="font-medium text-emerald-900"
                          >Equipment Required:</span
                        >
                        <div class="mt-1">
                          <span
                            v-if="
                              !testCatalog.equipment ||
                              testCatalog.equipment.length === 0
                            "
                            >N/A</span
                          >
                          <ul v-else class="list-disc list-inside">
                            <li
                              v-for="equip in testCatalog.equipment"
                              :key="equip.id"
                            >
                              {{ equip.name }}
                            </li>
                          </ul>
                        </div>
                      </div>
                      <div class="mt-2">
                        <span class="font-medium text-emerald-900"
                          >Lab Test Sites:</span
                        >
                        <div class="mt-1">
                          <span
                            v-if="
                              !testCatalog.lab_test_sites ||
                              testCatalog.lab_test_sites.length === 0
                            "
                            >N/A</span
                          >
                          <ul v-else class="list-disc list-inside">
                            <li
                              v-for="site in testCatalog.lab_test_sites"
                              :key="site.id"
                            >
                              {{ site.name }}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4">
              <h3 class="text-lg font-medium">Description</h3>
              <div
                class="mt-1.5 bg-gray-50 p-3 rounded border border-gray-100 border-dotted"
              >
                <div v-html="testCatalog?.description || 'N/A'"></div>
              </div>
            </div>
          </div>

          <div class="bg-white rounded mb-6 shadow-sm">
            <div class="w-full border-b border-gray-200 px-5 py-2">
              <h2 class="text-xl font-semibold text-gray-800">Measures</h2>
            </div>

            <div
              v-if="!testCatalog.measures || testCatalog.measures.length === 0"
              class="p-4 text-center text-gray-500 border rounded"
            >
              No measures available
            </div>

            <div v-else class="p-5">
              <div
                v-for="(measure, index) in testCatalog.measures"
                :key="measure.id"
                class="mb-6 p-4 bg-gray-50 rounded border border-gray-200"
              >
                <div class="flex justify-between items-center mb-3">
                  <h3 class="font-medium text-lg">
                    {{ measure.name || "Unnamed Measure" }} ({{
                      measure.unit || "No Unit"
                    }})
                  </h3>
                  <div
                    class="flex items-center bg-emerald-100 text-emerald-800 font-semibold rounded-full text-sm"
                  >
                    <div
                      class="p-1 bg-emerald-600 rounded-l-full text-emerald-100 mr-2"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                      >
                        <path
                          fill="none"
                          stroke="currentColor"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="1.5"
                          d="M6 16c-.93 0-1.395 0-1.776-.102a3 3 0 0 1-2.122-2.121C2 13.395 2 12.93 2 12s0-1.395.102-1.777a3 3 0 0 1 2.122-2.12C4.605 8 5.07 8 6 8m6 8h6c.93 0 1.395 0 1.776-.102a3 3 0 0 0 2.122-2.121C22 13.395 22 12.93 22 12s0-1.395-.102-1.777a3 3 0 0 0-2.122-2.12C19.396 8 18.93 8 18 8h-6M7 3h2m2 0H9m0 0v18m0 0H7m2 0h2"
                          color="currentColor"
                        />
                      </svg>
                    </div>
                    <div class="px-2">
                      {{ getMeasureTypeName(measure.measure_type_id) }}
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                  <div>
                    <span class="font-medium text-emerald-900">MOH Code:</span>
                    {{ measure.moh_code || "N/A" }}
                  </div>
                  <div>
                    <span class="font-medium text-emerald-900"
                      >LOINC Code:</span
                    >
                    {{ measure.loinc_code || "N/A" }}
                  </div>
                  <div>
                    <span class="font-medium text-emerald-900"
                      >NLIMS Code:</span
                    >
                    {{ measure.nlims_code || "N/A" }}
                  </div>
                </div>

                <div class="mt-3">
                  <h4 class="font-medium text-md mb-2">Reference Ranges</h4>

                  <div
                    v-if="
                      !measure.measure_ranges_attributes ||
                      measure.measure_ranges_attributes.length === 0
                    "
                    class="p-3 text-center text-gray-500 border border-gray-200 border-dotted rounded"
                  >
                    No ranges available
                  </div>

                  <div v-else>
                    <div
                      v-if="
                        getMeasureType(measure.measure_type_id)?.structure
                          ?.type === 'options'
                      "
                      class="bg-white border-gray-100 p-3 rounded border"
                    >
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div
                          v-for="range in measure.measure_ranges_attributes.filter(
                            (r) => r && 'value' in r
                          )"
                          :key="range.id"
                          class="flex justify-between"
                        >
                          <span class="font-medium text-emerald-900"
                            >{{ range.value || "N/A" }}:</span
                          >
                          <span>{{
                            range.interpretation || "No interpretation"
                          }}</span>
                        </div>
                      </div>
                    </div>

                    <div
                      v-else-if="
                        getMeasureType(measure.measure_type_id)?.structure
                          ?.type === 'ranges'
                      "
                      class="bg-white rounded border border-gray-100"
                    >
                      <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                          <thead>
                            <tr>
                              <th
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Sex
                              </th>
                              <th
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Age Range
                              </th>
                              <th
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Lower Range
                              </th>
                              <th
                                class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                Upper Range
                              </th>
                            </tr>
                          </thead>
                          <tbody class="bg-white divide-y divide-gray-200">
                            <tr
                              v-for="range in measure.measure_ranges_attributes.filter(
                                (r) => r && 'range_lower' in r
                              )"
                              :key="range.id"
                            >
                              <td class="px-3 py-2 whitespace-nowrap">
                                {{ range.sex || "Any" }}
                              </td>
                              <td class="px-3 py-2 whitespace-nowrap">
                                {{ range.age_min || "0" }} -
                                {{ range.age_max || "Any" }}
                              </td>
                              <td class="px-3 py-2 whitespace-nowrap">
                                {{ range.range_lower || "N/A" }}
                              </td>
                              <td class="px-3 py-2 whitespace-nowrap">
                                {{ range.range_upper || "N/A" }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div v-else class="bg-white p-3 rounded border">
                      <div
                        v-for="range in measure.measure_ranges_attributes"
                        :key="range.id"
                        class="mb-2 p-2 border-b border-gray-100"
                      >
                        <div v-if="'value' in range && range.value">
                          <span class="font-medium text-emerald-900"
                            >Value:</span
                          >
                          {{ range.value }}
                          <span v-if="range.interpretation" class="ml-2">
                            ({{ range.interpretation }})
                          </span>
                        </div>
                        <div
                          v-else-if="
                            'range_lower' in range || 'range_upper' in range
                          "
                        >
                          <div class="grid grid-cols-2 gap-2">
                            <div>
                              <span class="font-medium text-emerald-900"
                                >Lower range:</span
                              >
                              {{
                                "range_lower" in range
                                  ? range.range_lower
                                  : "N/A"
                              }}
                            </div>
                            <div>
                              <span class="font-medium text-emerald-900"
                                >Upper range:</span
                              >
                              {{
                                "range_upper" in range
                                  ? range.range_upper
                                  : "N/A"
                              }}
                            </div>
                            <div>
                              <span class="font-medium text-emerald-900"
                                >Sex:</span
                              >
                              {{ "sex" in range ? range.sex : "N/A" }}
                            </div>
                            <div>
                              <span class="font-medium text-emerald-900"
                                >Age:</span
                              >
                              {{ "age_min" in range ? range.age_min : "N/A" }} -
                              {{ "age_max" in range ? range.age_max : "N/A" }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ViewTestType, MeasureType } from "@/types";

definePageMeta({
  layout: "app",
  middleware: ["auth"],
  keepalive: false,
});

useHead({
  title: "View Test Type | Malawi Test Catalog",
});

const route = useRoute();
const loading = ref<boolean>(true);
const { $api, $metadata } = useNuxtApp();
const testCatalog = ref<ViewTestType>({} as ViewTestType);
const measureTypes = ref<MeasureType[]>([]);
const hasData = computed(() => {
  return testCatalog.value && !isObjectEmpty(testCatalog.value);
});

const hasMeasureTypes = computed(() => {
  return measureTypes.value && measureTypes.value.length > 0;
});

const handleError = (error: any) => {
  console.error("Error fetching test type:", error);
};

const getMeasureTypeName = (typeId: number): string => {
  // If typeId is falsy or types not loaded yet
  if (!typeId || !measureTypes.value || !measureTypes.value.length) {
    return "Unknown";
  }

  const measureType = measureTypes.value.find((type) => type.id === typeId);
  return measureType?.name || "Unknown";
};

const getMeasureType = (typeId: number): MeasureType | undefined => {
  // If typeId is falsy or types not loaded yet
  if (!typeId || !measureTypes.value || !measureTypes.value.length) {
    return undefined;
  }

  return measureTypes.value.find((type) => type.id === typeId);
};

const fetchMeasureTypes = async (): Promise<void> => {
  try {
    const measureTypesData = await $metadata.measure_types.getAll();

    // Check if the data is an array or has a data property
    if (Array.isArray(measureTypesData)) {
      measureTypes.value = measureTypesData;
    } else {
      measureTypes.value = measureTypesData.data || [];
    }

    if (!hasMeasureTypes.value) {
      console.warn("Fetched measure types is empty");
    }
  } catch (error) {
    console.error("Failed to load measure types:", error);
    measureTypes.value = []; // Reset to empty array on error
  }
};

const fetchTestType = async (): Promise<void> => {
  loading.value = true;

  try {
    if (!route.query.id) {
      loading.value = false;
      return;
    }

    const response: any = await $api.get(`test_types/${route.query.id}`);

    if (response) {
      testCatalog.value = response;
    } else {
      handleError(new Error("Invalid response format"));
    }
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => route.query.id,
  (newId, oldId) => {
    if (newId && newId !== oldId) {
      fetchTestType();
    }
  }
);

// Add activated hook to handle page revisits
onActivated(() => {
  // Refresh data when the page is revisited via navigation
  if (route.query.id) {
    fetchTestType();
    fetchMeasureTypes();
  }
});

// Navigation helper function
const router = useRouter();
const goBackToList = () => {
  // Clear data before navigating
  testCatalog.value = {} as ViewTestType;
  // Navigate to test types list
  router.push("/app/test-types");
};

onMounted(async () => {
  await fetchMeasureTypes();
  fetchTestType();
});

onBeforeUnmount(() => {
  testCatalog.value = {} as ViewTestType;
  loading.value = false;
});
</script>

<style scoped>
table th,
table td {
  text-align: left;
}
</style>
