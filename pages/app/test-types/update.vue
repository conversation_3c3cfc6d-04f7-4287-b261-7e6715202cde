<template>
    <div class="min-h-screen bg-gray-50 p-5">
        <div class="mx-auto">
            <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
                <SectionHeader icon="healthicons:test-tubes-outline" title="Update Test Type"
                    description="Edit test type with associated measures and specifications" />
                <BaseStepper
                    :steps="steps"
                    :initial-step="currentStep"
                    @validation-errors="getValidationErrors"
                    :step-statuses="stepStatuses"
                    @step-changed="handleStepChange"
                    @submit="updateTestType"
                    :data="testCatalog"
                    :submitting="loading">
                    <template #default="{ currentStep }">
                        <component :is="getStepComponent(currentStep).component"
                            v-bind="getStepComponent(currentStep).props" :ref="`stepper-current-${currentStep}`"
                            @update:selectedSpecimens="handleSpecimenUpdate"
                            @update:selectedOrganisms="handleOrganismsUpdate" />
                    </template>
                </BaseStepper>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTestType } from '@/composables/useTestType';
import type { Step } from "@/types/form";
import type { Specimen, LabTestSite, Equipment } from "@/types";

definePageMeta({
    layout: "app",
    middleware: ['auth']
});

useHead({
    title: "Update Test Type | Malawi Test Catalog",
});

const router = useRouter();
const route = useRoute();
const { $api, $toast } = useNuxtApp();

const {
    currentStep,
    loading,
    testCatalog,
    stepStatuses,
    validationErrors,
    handleStepChange,
    getValidationErrors,
    handleSpecimenUpdate,
    handleOrganismsUpdate,
    removeNullKeys,
    getSteps,
    prepareDataForSubmission
} = useTestType();

const steps = <Step[]>getSteps();

const getStepComponent = (currentStep: number) => {
    const step = steps.find((step) => step.id === currentStep);
    if (step) {
        return step;
    }
    return { component: null, props: {} };
};

const fetchTestType = async (): Promise<void> => {
    await $api
        .get(`test_types/${route.query.id}`)
        .then((response: any) => {
            if (response) {
                Object.assign(testCatalog, {
                    test_type: {
                        id: response.id,
                        name: response.name,
                        short_name: response.short_name,
                        description: response.description,
                        loinc_code: response.loinc_code,
                        moh_code: response.moh_code,
                        nlims_code: response.nlims_code,
                        targetTAT: response.targetTAT,
                        preferred_name: response.preferred_name,
                        scientific_name: response.scientific_name,
                        can_be_done_on_sex: response.can_be_done_on_sex,
                        test_category_id: response.test_category?.id || 0,
                        prevalence_threshold: response.prevalence_threshold,
                        created_at: response.created_at,
                        updated_at: response.updated_at,
                        assay_format: response.assay_format,
                        hr_cadre_required: response.hr_cadre_required,
                        iblis_mapping_name: response.iblis_mapping_name,
                    },
                    specimen_types:
                        response.specimen_types.map((specimen: Specimen) => specimen.id) ||
                        [],
                    measures: removeNullKeys(response.measures) || [],
                    organisms: response.organisms || [],
                    lab_test_sites: response.lab_test_sites.map((site: LabTestSite) => site.id) || [],
                    equipment: response.equipment.map((equipment: Equipment) => equipment.id) || [],
                });
            }
        })
        .catch((error: any) => {
            console.error(error);
            $toast.error("Error fetching test type");
        });
};

const updateTestType = async (): Promise<void> => {
    loading.value = true;

    await $api
        .put(`test_types/${route.query.id}`, {
            test_catalog: prepareDataForSubmission(),
        })
        .then((response: any) => {
            if (response) {
                $toast.success("Test type updated successfully");
                setTimeout(() => {
                    router.push("/app/test-types");
                }, 2000);
            }
        })
        .catch((error: any) => {
            $toast.error("Error updating test type");
            console.error("Error updating test type:", error);
        })
        .finally(() => {
            loading.value = false;
        });
};

onMounted(() => {
    fetchTestType();
});
</script>

<style scoped>
.editor {
    min-height: 200px;
    overflow-y: auto;
}
</style>
