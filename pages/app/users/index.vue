<template>
  <div class="px-4 py-8">
    <div>
      <h1 class="text-3xl font-semibold py-4">Users</h1>
    </div>
    <div class="w-full flex items-center justify-between bg-white border border-gray-100 rounded my-2 px-4 py-2">
      <div class="w-full flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="$router.push('/app/users/create')"
            class="cursor-pointer bg-emerald-600 hover:bg-emerald-700 transition duration-150 flex items-center text-emerald-100 px-2.5 py-2 rounded"
          >
            <Icon icon="mdi:plus" class="w-5 h-5 mr-1" />
            Add new
          </button>
          <BaseExcelExport :export-data="exportData" />
        </div>
        <div class="max-w-md w-full">
          <BaseSearchbar v-model="search" clearable placeholder="Search..." />
        </div>
      </div>
    </div>
    <div class="mt-5">
      <datatable
        table-class-name="datatable"
        :headers="headers"
        :items="filteredUsers"
        v-model:items-selected="itemsSelected"
        alternating
        :loading="loading"
        search-field="username"
        :search-value="search"
      >
        <template #loading>
          <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="currentColor"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="m36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
        </template>
        <template #item-actions="item">
          <div class="flex items-center space-x-3">
            <button
              @click="navigateTo(`/app/users/update/${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="fa:edit" class="w-5 h-5" />
            </button>
          </div>
        </template>
      </datatable>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Users | Malawi Test Catalog",
});

import type { Header, Item } from "vue3-easy-data-table";
import type { User } from "@/types";
import dayjs from "dayjs";

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const search = ref<string>("");
const itemsSelected = ref<Item[]>([]);
const headers: Header[] = [
  { text: "USERNAME", value: "username" },
  { text: "NAME", value: "app_name" },
  { text: "PARTNER", value: "partner" },
  { text: "DATE CREATED", value: "created_at" },
  { text: "ACTIONS", value: "actions" },
];
const users = ref<User[]>([]);

const filteredUsers = computed(() =>
  users.value.map((user: User) => ({
    ...user,
    created_at: dayjs(user.created_at).format(DATE_FORMAT),
  }))
);

const exportData = computed(() => ({
  name: `malawi-test-catalog-users-${dayjs().format(DATE_FORMAT)}`,
  headers: [
    "Malawi Test Catalog Users",
    "Report Date: " + dayjs().format(DATE_FORMAT),
  ],
  data: users.value.map((user: User) => ({
    USERNAME: user.username,
    NAME: user.app_name,
    PARTNER: user.partner,
    "CREATED AT": dayjs(user.created_at).format(DATE_FORMAT),
  })),
}));

const fetchUsers = async (): Promise<void> => {
  loading.value = true;
  try {
    const response: any = await $metadata.users.getAll();
    if (response) {
      users.value = response;
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchUsers();
});
</script>

<style scoped></style>
