<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="mdi:account-edit-outline"
          title="Update User"
          description="Modify details of the user account"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="username"
                  label="Username"
                  v-model="formData.username"
                  :error-message="usernameError"
                  required
                  @input="handleUsernameInput"
                />
                <BaseInput
                  name="app_name"
                  label="Full Name"
                  v-model="formData.app_name"
                  required
                />
                <BaseInput
                  name="partner"
                  label="Partner"
                  v-model="formData.partner"
                  required
                />
                <BaseInput
                  name="location"
                  label="Location"
                  v-model="formData.location"
                />
                <BaseInput
                  name="password"
                  label="New Password (optional)"
                  type="password"
                  v-model="formData.password"
                />
                <BaseInput
                  name="password_confirmation"
                  label="Confirm New Password"
                  type="password"
                  v-model="formData.password_confirmation"
                />
              </div>
              <BaseButton
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Update User | Malawi Test Catalog",
});

const { $metadata, $toast, $api } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const checkingUsername = ref<boolean>(false);
const usernameError = ref<string>("");
const user = ref<User | null>(null);
const originalUsername = ref<string>("");
let usernameTimeout: NodeJS.Timeout;

const formData = reactive({
  username: "",
  app_name: "",
  partner: "",
  location: "",
  password: "",
  password_confirmation: "",
  id: 0,
  created_at: "",
  updated_at: "",
});

const formConfig: FormConfig = {
  fields: [
    {
      name: "username",
      label: "Username",
      rules: { required: true, minLength: 4 },
    },
    {
      name: "app_name",
      label: "Full Name",
      rules: { required: true },
    },
    {
      name: "partner",
      label: "Partner",
      rules: { required: true },
    },
    {
      name: "location",
      label: "Location",
      rules: { required: false },
    },
    {
      name: "password",
      label: "New Password",
      rules: { required: false, minLength: 6 },
    },
    {
      name: "password_confirmation",
      label: "Confirm New Password",
      rules: { required: false, minLength: 6 },
    },
  ],
};

const fetchUser = async (): Promise<void> => {
  const id = route.params.id;
  if (!id) return;

  loading.value = true;
  try {
    const response: any = await $metadata.users.getById(Number(id));
    user.value = response;
    originalUsername.value = response.username;

    if (user.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          user.value &&
          key in user.value &&
          user.value[key as keyof User] !== undefined &&
          key !== 'password' &&
          key !== 'password_confirmation'
        ) {
          (formData as any)[key] = user.value[key as keyof User];
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch user data");
    setTimeout(() => {
      router.push("/app/users");
    }, 3000);
  } finally {
    loading.value = false;
  }
};

const handleUsernameInput = (): void => {
  clearTimeout(usernameTimeout);
  usernameError.value = "";
  
  usernameTimeout = setTimeout(() => {
    checkUsernameAvailability();
  }, 1000);
};

const checkUsernameAvailability = async (): Promise<void> => {
  // Skip check if username hasn't changed
  if (formData.username === originalUsername.value) {
    usernameError.value = "";
    return;
  }

  if (!formData.username || formData.username.trim() === '' || formData.username.length < 4) {
    usernameError.value = "";
    return;
  }

  checkingUsername.value = true;
  usernameError.value = "";

  try {
    const response = await $api.get(`users/check_username/${formData.username}`);
    usernameError.value = "";
  } catch (error: any) {
    if (error.status === 422) {
      usernameError.value = error.data.message;
    }
  } finally {
    checkingUsername.value = false;
  }
};

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (usernameError.value) {
    $toast.error("Please fix username error before submitting");
    return;
  }

  if (!valid || !user.value) return;

  if (formData.password && formData.password !== formData.password_confirmation) {
    $toast.error("Passwords do not match");
    return;
  }

  loading.value = true;
  try {
    const updateUser = {
      ...user.value,
      ...formData,
    };

    // Remove password fields if not provided
    if (!formData.password) {
      delete (updateUser as any).password;
      delete (updateUser as any).password_confirmation;
    }

    await $metadata.users.update(updateUser.id, updateUser);
    $toast.success("User updated successfully");
    setTimeout(() => {
      router.push("/app/users");
    }, 2000);
  } catch (error) {
    $toast.error("An error occurred while updating user");
  } finally {
    loading.value = false;
  }
};

onMounted(fetchUser);
</script>

<style scoped></style>
