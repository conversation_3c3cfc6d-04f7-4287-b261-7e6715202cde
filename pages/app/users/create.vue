<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="mdi:account-plus-outline"
          title="Create New User"
          description="Add details of a new user account"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="username"
                  label="Username"
                  v-model="user.username"
                  :error-message="usernameError"
                  required
                  @input="handleUsernameInput"
                />
                <BaseInput
                  name="app_name"
                  label="Full Name"
                  v-model="user.app_name"
                  required
                />
                <BaseInput
                  name="partner"
                  label="Partner"
                  v-model="user.partner"
                  required
                />
                <BaseInput
                  name="location"
                  label="Location"
                  v-model="user.location"
                  required
                />
                <BaseInput
                  name="password"
                  label="Password"
                  type="password"
                  v-model="user.password"
                  required
                />
                <BaseInput
                  name="password_confirmation"
                  label="Confirm Password"
                  type="password"
                  v-model="user.password_confirmation"
                  required
                />
              </div>
              <BaseButton
                class="mt-4"
                label="Submit"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from "@/types";
import type { FormConfig } from "@/types/input";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Create User | Malawi Test Catalog",
});

const { $metadata, $toast, $api } = useNuxtApp();
const loading = ref<boolean>(false);
const checkingUsername = ref<boolean>(false);
const usernameError = ref<string>("");
let usernameTimeout: NodeJS.Timeout;
const user = ref<User & { password: string; password_confirmation: string }>({
  id: 0,
  username: "",
  app_name: "",
  email: "",
  partner: "",
  location: "",
  password: "",
  password_confirmation: "",
  created_at: "",
  updated_at: "",
});

const formConfig: FormConfig = {
  fields: [
    {
      name: "username",
      label: "Username",
      rules: { required: true, minLength: 4 },
    },
    {
      name: "app_name",
      label: "Full Name",
      rules: { required: true },
    },
    {
      name: "email",
      label: "Email",
      rules: { required: true, email: true },
    },
    {
      name: "partner",
      label: "Partner",
      rules: { required: true },
    },
    {
      name: "location",
      label: "Location",
      rules: { required: false },
    },
    {
      name: "password",
      label: "Password",
      rules: { required: true, minLength: 6 },
    },
    {
      name: "password_confirmation",
      label: "Confirm Password",
      rules: { required: true, minLength: 6 },
    },
  ],
};

const submitForm = async (values: any, valid: boolean): Promise<void> => {
    if (user.value.password !== user.value.password_confirmation) {
      $toast.error("Passwords do not match");
      return;
    }
    
    loading.value = true;
    try {
      const response = await $metadata.users.create(user.value);
      if (response) {
        $toast.success("User created successfully");
        setTimeout(() => {
          navigateTo({ path: "/app/users" });
        }, 2000);
      }
    } catch (error: any) {
      $toast.error("An error occurred while creating user");
    } finally {
      loading.value = false;
    }
};

const handleUsernameInput = (): void => {
  clearTimeout(usernameTimeout);
  usernameError.value = "";
  
  usernameTimeout = setTimeout(() => {
    checkUsernameAvailability();
  }, 1000);
};

const checkUsernameAvailability = async (): Promise<void> => {
  if (!user.value.username || user.value.username.trim() === '') {
    usernameError.value = "";
    return;
  }

  checkingUsername.value = true;
  usernameError.value = "";

  try {
    const response = await $api.get(`users/check_username/${user.value.username}`);
    usernameError.value = "";
  } catch (error: any) {
    if (error.status === 422) {
      usernameError.value = error.data.message;
    }
  } finally {
    checkingUsername.value = false;
  }
};
</script>

<style scoped></style>
