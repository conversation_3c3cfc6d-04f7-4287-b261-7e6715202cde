<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="healthicons:medicines-outline"
          title="Update equipment"
          description="Modify details of the equipment"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="formData.name"
                  required
                />
                <BaseInput
                  name="loinc_code"
                  label="LONIC code"
                  v-model="formData.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="formData.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="formData.description"
                      contentType="html"
                    ></RichTextEditor>
                  </div>
                </div>
                <BaseDropdown
                  name="products"
                  label="Products"
                  :options="
                    products.map((product) => ({
                      id: product.id,
                      label: product.name,
                    }))
                  "
                  v-model="formData.products"
                  :isMultiple="true"
                  required
                />
              </div>
              <BaseButton
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Equipment, Product } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

const { $metadata, $toast } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const equipment = ref<Equipment | null>(null);
const products = ref<Product[]>([]);

const formData = reactive({
  name: "",
  description: "",
  loinc_code: "",
  moh_code: "",
  nlims_code: "",
  id: 0,
  created_at: "",
  updated_at: "",
  products: []
});

const formConfig: FormConfig = {
  fields: [
    { name: "name", label: "Name", rules: { required: true } },
    { name: "loinc_code", label: "LONIC code", rules: { required: false } },
    { name: "moh_code", label: "MOH code", rules: { required: false } },
    { name: "products", label: "Products", rules: { required: true } },
  ],
};

const fetchEquipment = async (): Promise<void> => {
  const id = route.params.id;
  if (!id) {
    $toast.error("Invalid equipment ID");
    setTimeout(() => {
      router.push("/app/equipments");
    }, 3000);
    return;
  }

  loading.value = true;
  try {
    const response: any = await $metadata.equipments.getById(Number(id));
    equipment.value = {...response, products: response.products.map((product: Product) => product.id)};

    if (equipment.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          equipment.value &&
          key in equipment.value &&
          equipment.value[key as keyof Equipment] !== undefined
        ) {
          (formData as any)[key] = equipment.value[key as keyof Equipment];
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch equipment data");
    setTimeout(() => {
      router.push("/app/equipments");
    }, 3000);
  } finally {
    loading.value = false;
  }
};

const fetchProducts = async (): Promise<void> => {
  try {
    const response: any = await $metadata.products.getAll();
    if (response) {
      products.value = response;
    }
  } catch (error: any) {
    console.error(error);
  }
};

onMounted(() => {
  fetchProducts();
  fetchEquipment();
});

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (!valid || !equipment.value) return;
  loading.value = true;
  try {
    const updateequipment = {
      ...equipment.value,
      ...formData,
    };
    await $metadata.equipments.update(updateequipment.id, updateequipment);
    $toast.success("Equipment updated successfully");
    setTimeout(() => {
      router.push("/app/equipments");
    }, 2000);
  } catch (error) {
    $toast.error("An error occurred while updating equipment");
  } finally {
    loading.value = false;
  }
};
</script>
