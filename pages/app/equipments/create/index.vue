<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="fluent:box-20-regular"
          title="Create equipment"
          description="Add details of the new equipment"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="equipment.name"
                  required
                />
                <BaseInput
                  name="lonic_code"
                  label="LONIC code"
                  v-model="equipment.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="equipment.moh_code"
                />

                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="equipment.description"
                      contentType="html"
                    >
                    </RichTextEditor>
                  </div>
                </div>
                <BaseDropdown
                  name="products"
                  label="Products"
                  :options="
                    products.map((product) => ({
                      id: product.id,
                      label: product.name,
                    }))
                  "
                  v-model="equipment.products"
                  :isMultiple="true"
                  required
                />
              </div>
              <BaseButton
                class="mt-4"
                label="Submit"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Equipment, Product } from "@/types";
import type { FormConfig } from "@/types/input";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Create equipment | Malawi Test Catalog",
  meta: [{ name: "description", content: "Create equipment details" }],
});

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const products = ref<Product[]>([]);
const equipment = ref<Equipment>({
  name: "",
  description: "",
  id: 0,
  created_at: "",
  updated_at: "",
  moh_code: null,
  nlims_code: null,
  loinc_code: null,
});

const formConfig: FormConfig = {
  fields: [
    {
      name: "name",
      label: "Name",
      rules: { required: true },
    },
    {
      name: "lonic_code",
      label: "LONIC code",
      rules: { required: false },
    },
    {
      name: "moh_code",
      label: "MOH code",
      rules: { required: false },
    },
    {
      name: "products",
      label: "Products",
      rules: { required: true },
    },
  ],
};

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  console.log(values)
  if (valid) {
    loading.value = true;
    try {
      const response = await $metadata.equipments.create(equipment.value);
      if (response) {
        $toast.success("Equipment created successfully");
        navigateTo({ path: "/app/equipments" });
      }
    } catch (error: any) {
      $toast.error("An error occurred while creating equipment");
    } finally {
      loading.value = false;
    }
  }
};

const fetchProducts = async (): Promise<void> => {
  try {
    const response: any = await $metadata.products.getAll();
    if (response) {
      products.value = response;
    }
  } catch (error: any) {
    console.log(error);
  }
};

onMounted(() => {
  fetchProducts();
});
</script>
