<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="fluent:panel-left-text-20-regular"
          :title="testPanel.name || 'Test Panel Details'"
          description="View test panel details"
        />

        <div v-if="loading" class="p-6 flex justify-center">
          <svg
            class="animate-spin h-8 w-8 text-emerald-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>

        <div v-else class="p-6">
          <div class="space-y-8">
            <div>
              <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3">
                <h2 class="text-xl font-semibold">Basic Information</h2>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div>
                  <h3 class="text-sm font-medium text-gray-500">Name</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.name }}</p>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-500">Short Name</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.short_name }}</p>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-500">Preferred Name</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.preferred_name || 'N/A' }}</p>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-500">Scientific Name</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.scientific_name || 'N/A' }}</p>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-500">LOINC Code</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.loinc_code || 'N/A' }}</p>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-500">NLIMS Code</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.nlims_code || 'N/A' }}</p>
                </div>

                <div>
                  <h3 class="text-sm font-medium text-gray-500">MOH Code</h3>
                  <p class="mt-1 text-base text-gray-900">{{ testPanel.moh_code || 'N/A' }}</p>
                </div>

                <div class="col-span-2">
                  <h3 class="text-sm font-medium text-gray-500">Description</h3>
                  <div class="mt-1 text-base text-gray-900" v-html="testPanel.description"></div>
                </div>
              </div>
            </div>

            <div>
              <div class="bg-gray-50 border-b border-gray-100 px-2 py-2 rounded mb-3">
                <h2 class="text-xl font-semibold">Test Types</h2>
              </div>

              <div v-if="testPanel.test_types.length === 0" class="text-gray-500 italic">
                No test types associated with this panel.
              </div>

              <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div v-for="testType in testPanel.test_types" :key="testType.id" class="border border-gray-200 rounded-md p-3">
                  <h3 class="font-medium text-gray-900">{{ testType.name }}</h3>
                  <p class="text-sm text-gray-500">{{ testType.short_name }}</p>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-4">
              <BaseButton
                label="Edit"
                @click="router.push(`/app/test-panels/update?id=${testPanel.id}`)"
                variant="secondary"
              />
              <BaseButton
                label="Back to List"
                @click="router.push('/app/test-panels')"
                variant="outline"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTestPanel } from '@/composables/useTestPanel';
import Endpoints from '@/utils/endpoints';

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "View Test Panel | Malawi Test Catalog",
});

const router = useRouter();
const route = useRoute();
const { $toast, $api } = useNuxtApp();
const { testPanel, loading }: any = useTestPanel();

const fetchTestPanel = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await $api.get(`${Endpoints.TEST_PANELS}/${route.query.id}`);
    if (response) {
      Object.assign(testPanel, response);
    }
  } catch (error) {
    console.error("Failed to fetch test panel:", error);
    $toast.error("Failed to fetch test panel");
  } finally {
    loading.value = false;
  }
};

onMounted(fetchTestPanel);
</script>
