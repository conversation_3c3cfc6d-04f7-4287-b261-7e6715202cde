<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          icon="fluent:panel-left-text-20-regular"
          title="Create Test Panel"
          description="Add a new test panel with associated test types"
        />
        <div class="p-6">
          <CoreForm
            :config="formConfig"
            @submit="submitForm"
            :validate-on-submit="true"
            :validate-on-change="true"
            :model-value="testPanel"
          >
            <template #default>
              <div class="space-y-8">
                <TestPanelsBasicInformation
                  :test-panel="testPanel"
                  :errors="validationErrors"
                />

                <TestPanelsTestTypes
                  :test-panel="testPanel"
                  @update:selectedTestTypes="handleTestTypesUpdate"
                />

                <div class="flex justify-end mt-6">
                  <BaseButton
                    type="submit"
                    label="Submit"
                    :loading="loading"
                  />
                </div>
              </div>
            </template>
          </CoreForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTestPanel } from '@/composables/useTestPanel';
import type { FormConfig } from '@/types/input';
import TestPanelsBasicInformation from '@/components/test-panels/BasicInformation.vue';
import TestPanelsTestTypes from '@/components/test-panels/TestTypes.vue';
import CoreForm from '@/components/CoreForm.vue';
import { onMounted, onUnmounted } from 'vue';

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Create Test Panel | Malawi Test Catalog",
});

const router = useRouter();
const { $metadata, $toast } = useNuxtApp();

const handleDescriptionUpdate = (event: CustomEvent) => {
  if (event.detail && event.detail.description !== undefined) {
  }
};

onMounted(() => {
  window.addEventListener('description-updated', handleDescriptionUpdate as EventListener);
});

onUnmounted(() => {
  window.removeEventListener('description-updated', handleDescriptionUpdate as EventListener);
});

const {
  loading,
  testPanel,
  validationErrors,
  handleTestTypesUpdate,
  getValidationErrors,
  prepareDataForSubmission,
} = useTestPanel();

const formConfig: FormConfig = {
  fields: [
    {
      name: "name",
      label: "Name",
      rules: { required: true },
    },
    {
      name: "short_name",
      label: "Short Name",
      rules: { required: true },
    },
    {
      name: "description",
      label: "Description",
      rules: { required: true },
    },
    {
      name: "test_types",
      label: "Test Types",
      rules: {
        custom: (_: any) => {
          if (!testPanel.test_types || testPanel.test_types.length === 0) {
            return "At least one test type must be selected";
          }
          return true;
        }
      },
    },
  ],
};

const submitForm = async (formValues: any, valid: boolean): Promise<void> => {
  const errors = validationErrors as Record<string, { message: string }>;

  if (!testPanel.description || testPanel.description.trim() === '') {
    errors.description = { message: "Description is required" };
    valid = false;
  } else if (errors.description) {
    delete errors.description;
  }

  if (!testPanel.test_types || testPanel.test_types.length === 0) {
    errors.test_types = { message: "At least one test type must be selected" };
    valid = false;
  } else if (errors.test_types) {
    delete errors.test_types;
  }

  if (!valid) {
    console.error("Form validation failed:", validationErrors);
    return;
  }

  if (testPanel.description === null) {
    testPanel.description = "";
  }

  loading.value = true;
  try {
    const data = prepareDataForSubmission();
    const response = await $metadata.test_panels.create({
      test_panel: data
    });

    if (response) {
      $toast.success("Test panel created successfully");
      setTimeout(() => {
        router.push("/app/test-panels");
      }, 2000);
    }
  } catch (error: any) {
    console.error("Error creating test panel:", error);
    $toast.error("Failed to create test panel");

    if (error.validation) {
      getValidationErrors(error.validation);
    }
  } finally {
    loading.value = false;
  }
};
</script>
