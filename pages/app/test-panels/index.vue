<template>
  <div class="px-4 py-8">
    <div>
      <h1 class="text-3xl font-semibold py-4">Test Panels</h1>
    </div>
    <div class="w-full flex items-center justify-between bg-white border border-gray-100 rounded my-2 px-4 py-2">
      <div class="w-80">
        <BaseSearchbar v-model="search" placeholder="Search test panels..." />
      </div>
      <BaseButton icon-start="mdi:plus" @click="navigateTo('/app/test-panels/create')" label="Add Test Panel" />
    </div>
    <div class="overflow-hidden">
      <datatable
        table-class-name="datatable"
        :headers="headers"
        :items="filteredTestPanels"
        :loading="loading"
        :current-page="pagination.currentPage"
        :rows-per-page="pagination.perPage"
        @update:current-page="handlePageChange"
      >
      <template #loading>
          <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="oklch(0.596 0.145 163.225)"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="M36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
        </template>
        <template #item-description="item">
          <div v-html="item.description"></div>
        </template>
        <template #item-test_types="item">
          <div class="line-clamp-1">{{ item.test_types !== null && item.test_types.map((type: { name: string; }) => type.name).join(', ') }}</div>
        </template>
        <template #item-actions="item">
          <div class="flex items-center space-x-3">
            <button
              @click="navigateTo(`/app/test-panels/view?id=${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="lets-icons:view-alt-duotone" class="w-5 h-5 mr-1" />
            </button>
            <button
              @click="navigateTo(`/app/test-panels/update?id=${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="fa:edit" class="w-5 h-5" />
            </button>
            <DialogsItemDeleteDialog
              :property="{ id: item.id, name: 'test panels', endpoint: 'test_panels' }"
              @confirm="confirmDeletion"
            >
              <button class="cursor-pointer">
                <Icon icon="mdi:delete" class="w-5 h-5 mr-1" />
              </button>
            </DialogsItemDeleteDialog>
          </div>
        </template>
      </datatable>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TestPanel } from '@/types';
import type { Header } from 'vue3-easy-data-table';

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Test Panels | Malawi Test Catalog",
});

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const testPanels = ref<TestPanel[]>([]);
const search = ref<string>("");

const headers: Header[] = [
  { text: "NAME", value: "name" },
  { text: "SHORT NAME", value: "short_name" },
  { text: "DESCRIPTION", value: "description" },
  { text: "TEST TYPES", value: "test_types" },
  { text: "ACTIONS", value: "actions" },
];

const pagination = reactive({
  currentPage: 1,
  perPage: 10,
  total: 0,
});

const filteredTestPanels = computed(() => {
  if (!search.value) return testPanels.value;
  return testPanels.value.filter((panel) =>
    panel.name.toLowerCase().includes(search.value.toLowerCase()) ||
    panel.short_name.toLowerCase().includes(search.value.toLowerCase()) ||
    (panel.description && panel.description.toLowerCase().includes(search.value.toLowerCase()))
  );
});

const fetchTestPanels = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await $metadata.test_panels.getAll();
    testPanels.value = response.data || response;
    pagination.total = testPanels.value.length;
  } catch (error) {
    console.error("Failed to fetch test panels:", error);
    $toast.error("Failed to fetch test panels");
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page: number): void => {
  pagination.currentPage = page;
};

const confirmDeletion = async (): Promise<void> => {
  await fetchTestPanels();
};

onMounted(fetchTestPanels);
</script>
