<script setup lang="ts">
import dayjs from 'dayjs';

definePageMeta({
    layout: "app",
    middleware: ['auth']
});

useHead({
    title: "Dashboard | Malawi Test Catalog"
});

const { $metadata, $api } = useNuxtApp();
const loading = ref(false);

// Statistics data
const metrics = ref([
    {
        title: 'Specimens',
        count: 0,
        icon: 'healthicons:microscope-with-specimen-outline',
        description: 'Specimen types in catalog',
        route: '/app/specimens',
        color: 'emerald',
        trend: '+12%',
        trendUp: true
    },
    {
        title: 'Test Types',
        count: 0,
        icon: 'healthicons:test-tubes-outline',
        description: 'Available test types',
        route: '/app/test-types',
        color: 'blue',
        trend: '+8%',
        trendUp: true
    },
    {
        title: 'Test Panels',
        count: 0,
        icon: 'healthicons:i-schedule-school-date-time',
        description: 'Configured test panels',
        route: '/app/test-panels',
        color: 'purple',
        trend: '+5%',
        trendUp: true
    },
    {
        title: 'Drugs',
        count: 0,
        icon: 'healthicons:medicines-outline',
        description: 'Drug catalog entries',
        route: '/app/drugs',
        color: 'orange',
        trend: '+15%',
        trendUp: true
    }
]);

// Quick stats for overview cards
const quickStats = ref([
    {
        title: 'Active Tests Today',
        value: '156',
        icon: 'healthicons:medical-records-outline',
        color: 'emerald',
        description: 'Tests processed today'
    },
    {
        title: 'Lab Utilization',
        value: '87%',
        icon: 'healthicons:health-worker-form-outline',
        color: 'blue',
        description: 'Current lab capacity'
    },
    {
        title: 'Avg. TAT',
        value: '2.4h',
        icon: 'healthicons:timer-outline',
        color: 'orange',
        description: 'Average turnaround time'
    },
    {
        title: 'Quality Score',
        value: '98.2%',
        icon: 'healthicons:star-outline',
        color: 'purple',
        description: 'Quality compliance rate'
    }
]);

// System health indicators
const systemHealth = ref([
    {
        name: 'Database',
        status: 'healthy',
        uptime: '99.9%',
        lastCheck: '2 min ago'
    },
    {
        name: 'API Services',
        status: 'healthy',
        uptime: '99.7%',
        lastCheck: '1 min ago'
    },
    {
        name: 'Lab Integration',
        status: 'warning',
        uptime: '95.2%',
        lastCheck: '5 min ago'
    }
]);

// Mock data for dashboard
const fetchDashboardData = async () => {
    loading.value = true;

    // Simulate API loading delay
    await new Promise(resolve => setTimeout(resolve, 800));

    try {
        // Mock data for statistics
        metrics.value[0].count = 65; // Specimens
        metrics.value[1].count = 123; // Test Types
        metrics.value[2].count = 8; // Test Panels
        metrics.value[3].count = 23; // Drugs

    } catch (error) {
        console.error('Error loading dashboard data:', error);
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchDashboardData();
});
</script>

<template>
    <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <!-- Header Section -->
        <div class="bg-white shadow-sm border-b border-gray-200 relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-5">
                <svg class="w-full h-full" viewBox="0 0 400 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="medicalPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                            <path d="M20 10 L20 30 M10 20 L30 20" stroke="#059669" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="20" cy="20" r="15" stroke="#059669" stroke-width="1" fill="none" opacity="0.3"/>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#medicalPattern)"/>
                </svg>
            </div>

            <!-- Decorative Circles -->
            <div class="absolute top-4 right-4 w-32 h-32 bg-emerald-100 rounded-full opacity-20 blur-xl"></div>
            <div class="absolute bottom-4 left-4 w-24 h-24 bg-blue-100 rounded-full opacity-20 blur-lg"></div>
            <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-purple-100 rounded-full opacity-15 blur-md"></div>

            <div class="px-6 py-8 relative z-10">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-6 lg:mb-0">
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">
                            Welcome back!
                        </h1>
                        <p class="text-lg text-gray-600">
                            Manage and monitor your laboratory test catalog efficiently
                        </p>
                        <div class="flex items-center mt-3 text-sm text-gray-500">
                            <Icon icon="heroicons:calendar-days-20-solid" class="h-4 w-4 mr-2" />
                            <span>{{ dayjs().format('dddd, MMMM D, YYYY') }}</span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button
                            @click="fetchDashboardData"
                            :disabled="loading"
                            class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 px-6 py-3 rounded font-medium transition-colors duration-200 flex items-center justify-center"
                        >
                            <Icon icon="heroicons:arrow-path-20-solid" class="h-5 w-5 mr-2" :class="{ 'animate-spin': loading }" />
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-6 py-8">
            <!-- Main Statistics Cards -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Catalog Overview</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div
                        v-for="metric in metrics"
                        :key="metric.title"
                        @click="$router.push(metric.route)"
                        class="bg-white rounded shadow-sm p-6 hover:shadow-md transition-all duration-200 cursor-pointer group relative overflow-hidden"
                    >
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-4">
                                    <div :class="`bg-${metric.color}-100 text-${metric.color}-600 p-3 rounded group-hover:scale-110 transition-transform duration-200`">
                                        <Icon :icon="metric.icon" class="h-6 w-6" />
                                    </div>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-1">
                                    {{ loading ? '...' : metric.count.toLocaleString() }}
                                </h3>
                                <p class="text-sm font-medium text-gray-600 mb-2">{{ metric.title }}</p>
                                <p class="text-xs text-gray-500">{{ metric.description }}</p>
                                <div class="flex items-center mt-3">
                                    <Icon
                                        :icon="metric.trendUp ? 'heroicons:arrow-trending-up-20-solid' : 'heroicons:arrow-trending-down-20-solid'"
                                        :class="`h-4 w-4 ${metric.trendUp ? 'text-emerald-500' : 'text-red-500'} mr-1`"
                                    />
                                    <span :class="`text-xs font-medium ${metric.trendUp ? 'text-emerald-600' : 'text-red-600'}`">
                                        {{ metric.trend }}
                                    </span>
                                    <span class="text-xs text-gray-500 ml-1">from last month</span>
                                </div>
                            </div>
                        </div>
                        <div :class="`absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-${metric.color}-500 to-${metric.color}-600 rounded-bl-3xl opacity-10 group-hover:opacity-20 transition-opacity duration-200`"></div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Grid -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Quick Statistics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div
                        v-for="stat in quickStats"
                        :key="stat.title"
                        class="bg-white rounded shadow-sm p-6"
                    >
                        <div class="flex items-center justify-between mb-4">
                            <div :class="`bg-${stat.color}-100 text-${stat.color}-600 p-2 rounded`">
                                <Icon :icon="stat.icon" class="h-5 w-5" />
                            </div>
                            <span class="text-2xl font-bold text-gray-900">{{ stat.value }}</span>
                        </div>
                        <h3 class="text-sm font-medium text-gray-900 mb-1">{{ stat.title }}</h3>
                        <p class="text-xs text-gray-500">{{ stat.description }}</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded shadow-sm">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">System Health</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div
                                v-for="system in systemHealth"
                                :key="system.name"
                                class="flex items-center justify-between"
                            >
                                <div class="flex items-center space-x-3">
                                    <div
                                        :class="`w-3 h-3 rounded-full ${
                                            system.status === 'healthy' ? 'bg-emerald-500' :
                                            system.status === 'warning' ? 'bg-yellow-500' :
                                            'bg-red-500'
                                        }`"
                                    ></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ system.name }}</p>
                                        <p class="text-xs text-gray-500">{{ system.lastCheck }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ system.uptime }}</p>
                                    <p class="text-xs text-gray-500">Uptime</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
