<template>
  <div class="px-4 py-8">
    <div>
      <h1 class="text-3xl font-semibold mb-6">Drugs</h1>
      <div class="w-full flex items-center justify-between flex-wrap gap-4">
        <div class="flex items-center space-x-4">
          <button
            @click="$router.push('/app/drugs/create')"
            class="cursor-pointer bg-emerald-600 hover:bg-emerald-700 transition duration-150 flex items-center text-emerald-100 px-2.5 py-2 rounded"
          >
            <Icon icon="mdi:plus" class="w-5 h-5 mr-1" />
            Add new
          </button>
          <BaseExcelExport :export-data="exportData" />
        </div>
        <div class="max-w-md w-full">
          <BaseSearchbar v-model="search" placeholder="Search..." />
        </div>
      </div>
    </div>
    <div class="mt-5">
      <div
        class="w-full flex items-center justify-between bg-white p-2 border border-gray-100 rounded my-2 px-2"
      >
        <div class="w-full flex items-center flex-wrap md:flex-nowrap gap-3">
          <div class="flex items-center flex-wrap sm:flex-nowrap gap-2">
            <p class="whitespace-nowrap"><span class="text-base font-medium">Date created:</span></p>
            <div class="w-full sm:w-auto">
              <Datepicker
                required
                position="left"
                placeholder="select start & end date"
                :range="true"
                input-class-name="datepicker w-full"
                v-model="dateRange"
                :format="DATE_PICKER"
                :maxDate="new Date()"
              />
            </div>
            <button
              v-if="dateRange && dateRange.length"
              @click="dateRange = []"
              class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm"
            >
              Clear
            </button>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <datatable
          table-class-name="datatable w-full"
          :headers="headers"
          :items="filteredDrugs"
          v-model:items-selected="itemsSelected"
          alternating
          :loading="loading"
        >
        <template #loading>
          <svg
            class="w-15 h-15 text-emerald-600"
            width="38"
            height="38"
            viewBox="0 0 38 38"
            xmlns="http://www.w3.org/2000/svg"
            stroke="oklch(0.596 0.145 163.225)"
          >
            <g fill="none" fill-rule="evenodd">
              <g transform="translate(1 1)" stroke-width="2">
                <circle stroke-opacity=".5" cx="18" cy="18" r="18" />
                <path d="M36 18c0-9.94-8.06-18-18-18">
                  <animateTransform
                    attributeName="transform"
                    type="rotate"
                    from="0 18 18"
                    to="360 18 18"
                    dur="1s"
                    repeatCount="indefinite"
                  />
                </path>
              </g>
            </g>
          </svg>
        </template>
        <template #item-description="item">
          <div v-html="item.description"></div>
        </template>
        <template #item-actions="item">
          <div class="flex items-center space-x-3">
            <DrugsViewDialog :drug="item" />
            <button
              @click="navigateTo(`/app/drugs/update/${item.id}`)"
              class="cursor-pointer"
            >
              <Icon icon="fa:edit" class="w-5 h-5" />
            </button>
            <DialogsItemDeleteDialog
              :property="{ id: item.id, name: 'drugs', endpoint: 'drugs' }"
              @confirm="confirmDeletion"
            >
              <button class="cursor-pointer">
                <Icon icon="mdi:delete" class="w-5 h-5 mr-1" />
              </button>
            </DialogsItemDeleteDialog>
          </div>
        </template>
      </datatable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Drugs | Malawi Test Catalog",
});

import type { Header, Item } from "vue3-easy-data-table";
import type { Drug } from "@/types";
import dayjs from "dayjs";
import { DATE_FORMAT, DATE_PICKER } from '@/utils/constants';

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const search = ref<string>("");
const itemsSelected = ref<Item[]>([]);
const dateRange = ref([]);
const headers: Header[] = [
  { text: "NAME", value: "name" },
  { text: "SHORT NAME", value: "short_name" },
  { text: "OTHER NAMES", value: "preferred_name" },
  { text: "DESCRIPTION", value: "description" },
  { text: "NLIMS CODE", value: "nlims_code" },
  { text: "DATE CREATED", value: "created_at" },
  { text: "ACTIONS", value: "actions" },
];
const drugs = ref<Drug[]>([]);

const filteredDrugs = computed(() => {
  // First filter by search term
  let filtered = drugs.value.filter((drug: Drug) => {
    if (!search.value) return true;
    const searchLower = search.value.toLowerCase();
    return (
      drug.name?.toLowerCase().includes(searchLower) ||
      drug.short_name?.toLowerCase().includes(searchLower) ||
      drug.preferred_name?.toLowerCase().includes(searchLower) ||
      drug.nlims_code?.toLowerCase().includes(searchLower) ||
      drug.description?.toLowerCase().includes(searchLower)
    );
  });

  // Then filter by date range if selected
  if (dateRange.value?.length === 2 && dateRange.value[0] && dateRange.value[1]) {
    const startDate = dayjs(dateRange.value[0]).startOf('day');
    const endDate = dayjs(dateRange.value[1]).endOf('day');

    filtered = filtered.filter((drug: Drug) => {
      const createdDate = dayjs(drug.created_at);
      return createdDate.isAfter(startDate) && createdDate.isBefore(endDate);
    });
  }

  // Format dates for display
  return filtered.map((drug: Drug) => ({
    ...drug,
    created_at: dayjs(drug.created_at).format(DATE_FORMAT),
  }));
});

const exportData = computed(() => ({
  name: `malawi-test-catalog-drugs-${dayjs().format(DATE_FORMAT)}`,
  headers: [
    "Malawi Test Catalog Drugs",
    "Report Date: " + dayjs().format(DATE_FORMAT),
  ],
  data: drugs.value.map((drug) => ({
    NAME: drug.name,
    "SHORT NAME": drug.short_name,
    "OTHER NAMES": drug.preferred_name,
    DESCRIPTION: drug.description,
    "NLIMS CODE": drug.nlims_code,
    "DATE CREATED": dayjs(drug.created_at).format(DATE_FORMAT),
  })),
}));

const fetchDrugs = async (): Promise<void> => {
  loading.value = true;
  try {
    const response: any = await $metadata.drugs.getAll();
    if (response) {
      drugs.value = response;
    }
  } catch (error) {
    console.error(error);
    $toast.error("An error occurred while fetching drugs");
  } finally {
    loading.value = false;
  }
};

const confirmDeletion = async () => {
  $toast.success(`Drug deleted successfully`);
  fetchDrugs();
};

onMounted(() => {
  fetchDrugs();
});
</script>

<style scoped></style>
