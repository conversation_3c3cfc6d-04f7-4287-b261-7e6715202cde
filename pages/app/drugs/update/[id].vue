<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="healthicons:medicines-outline"
          title="Update Drug"
          description="Modify details of the drug"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="formData.name"
                  required
                />
                <BaseInput
                  name="short_name"
                  label="Short name"
                  v-model="formData.short_name"
                />
                <BaseInput
                  name="preferred_name"
                  label="Preferred name"
                  v-model="formData.preferred_name"
                  required
                />
                <BaseInput
                  name="scientific_name"
                  label="Scientific name"
                  v-model="formData.scientific_name"
                />
                <BaseInput
                  name="loinc_code"
                  label="LONIC code"
                  v-model="formData.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="formData.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="formData.description"
                      contentType="html"
                    ></RichTextEditor>
                  </div>
                </div>
              </div>
              <BaseButton
                class="mt-4"
                label="Update"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Drug } from "@/types";
import type { FormConfig } from "@/types/input";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

const { $metadata, $toast } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const loading = ref<boolean>(false);
const drug = ref<Drug | null>(null);
const formData = reactive({
  name: "",
  short_name: "",
  description: "",
  preferred_name: "",
  scientific_name: "",
  loinc_code: "",
  moh_code: "",
  nlims_code: "",
  id: 0,
  created_at: "",
  updated_at: "",
});

const formConfig: FormConfig = {
  fields: [
    { name: "name", label: "Name", rules: { required: true } },
    { name: "short_name", label: "Short name", rules: { required: false } },
    {
      name: "preferred_name",
      label: "Preferred name",
      rules: { required: true },
    },
    {
      name: "scientific_name",
      label: "Scientific name",
      rules: { required: false },
    },
    { name: "loinc_code", label: "LONIC code", rules: { required: false } },
    { name: "moh_code", label: "MOH code", rules: { required: false } },
  ],
};

const fetchDrugs = async (): Promise<void> => {
  const id = route.params.id;
  if (!id) {
    $toast.error("Invalid drug ID");
    setTimeout(() => {
      router.push("/app/drugs");
    }, 3000);
    return;
  }

  loading.value = true;
  try {
    const response: any = await $metadata.drugs.getById(Number(id));
    drug.value = response;

    if (drug.value !== null) {
      Object.keys(formData).forEach((key) => {
        if (
          drug.value &&
          key in drug.value &&
          drug.value[key as keyof Drug] !== undefined
        ) {
          (formData as any)[key] = drug.value[key as keyof Drug];
        }
      });
    }
  } catch (error) {
    $toast.error("Failed to fetch drug data");
    setTimeout(() => {
      router.push("/app/drugs");
    }, 3000);
  } finally {
    loading.value = false;
  }
};

onMounted(fetchDrugs);

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (!valid || !drug.value) return;

  loading.value = true;
  try {
    const updateDrug = {
      ...drug.value,
      ...formData,
    };
    await $metadata.drugs.update(updateDrug.id, updateDrug);
    $toast.success("Drug updated successfully");
    setTimeout(() => {
      router.push("/app/drugs");
    }, 3000);
  } catch (error) {
    $toast.error("An error occurred while updating drug");
  } finally {
    loading.value = false;
  }
};
</script>
