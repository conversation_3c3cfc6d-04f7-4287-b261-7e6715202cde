<template>
  <div class="min-h-screen bg-gray-50 p-5">
    <div class="mx-auto">
      <div class="bg-white rounded-lg border border-gray-100 overflow-hidden">
        <SectionHeader
          mode="primary"
          icon="healthicons:medicines-outline"
          title="Create Drug"
          description="Add details of the new drug"
        />
        <div class="w-full py-5 px-5">
          <BaseForm :config="formConfig" @submit="submitForm">
            <template v-slot="{ form }">
              <div class="w-full grid grid-cols-2 gap-5">
                <BaseInput
                  name="name"
                  label="Name"
                  v-model="drug.name"
                  required
                />
                <BaseInput
                  name="short_name"
                  label="Short name"
                  v-model="drug.short_name"
                />
                <BaseInput
                  name="preferred_name"
                  label="Preferred name"
                  v-model="drug.preferred_name"
                  required
                />
                <BaseInput
                  name="scientific_name"
                  label="Scientific name"
                  v-model="drug.scientific_name"
                />
                <BaseInput
                  name="lonic_code"
                  label="LONIC code"
                  v-model="drug.loinc_code"
                />
                <BaseInput
                  name="moh_code"
                  label="MOH code"
                  v-model="drug.moh_code"
                />
                <div>
                  <label
                    for="description"
                    class="block font-medium text-gray-700 mb-2"
                    >Description</label
                  >
                  <div class="overflow-hidden">
                    <RichTextEditor
                      theme="snow"
                      class="editor"
                      v-model:content="drug.description"
                      contentType="html"
                    >
                    </RichTextEditor>
                  </div>
                </div>
              </div>
              <BaseButton
                class="mt-4"
                label="Submit"
                :loading="loading"
                type="submit"
              />
            </template>
          </BaseForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Drug } from "@/types";
import type { FormConfig } from "@/types/input";

definePageMeta({
  layout: "app",
  middleware: ['auth']
});

useHead({
  title: "Create Drug | Malawi Test Catalog",
  meta: [{ name: "description", content: "Create drug details" }],
});

const { $metadata, $toast } = useNuxtApp();
const loading = ref<boolean>(false);
const drug = ref<Drug>({
  name: "",
  short_name: "",
  description: "",
  id: 0,
  created_at: "",
  updated_at: "",
  moh_code: null,
  nlims_code: null,
  loinc_code: null,
  preferred_name: null,
  scientific_name: null,
});

const formConfig: FormConfig = {
  fields: [
    {
      name: "name",
      label: "Name",
      rules: { required: true },
    },
    {
      name: "short_name",
      label: "Short name",
      rules: { required: false },
    },
    {
      name: "preferred_name",
      label: "Preferred name",
      rules: { required: true },
    },
    {
      name: "scientific_name",
      label: "Scientific name",
      rules: { required: false },
    },
    {
      name: "lonic_code",
      label: "LONIC code",
      rules: { required: false },
    },
    {
      name: "moh_code",
      label: "MOH code",
      rules: { required: false },
    },
  ],
};

const submitForm = async (values: any, valid: boolean): Promise<void> => {
  if (valid) {
    loading.value = true;
    try {
      const response = await $metadata.drugs.create(drug.value);
      if (response) {
        $toast.success("drug created successfully");
        navigateTo({ path: "/app/drugs" });
      }
    } catch (error: any) {
      $toast.error("An error occurred while creating drug");
    } finally {
      loading.value = false;
    }
  }
};
</script>
