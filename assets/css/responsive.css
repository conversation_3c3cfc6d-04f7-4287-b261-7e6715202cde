/* Responsive utilities */
.table-responsive-wrapper {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    .md-hide {
        display: none;
    }

    .md-full-width {
        width: 100%;
    }

    .md-text-center {
        text-align: center;
    }

    .md-flex-col {
        flex-direction: column;
    }

    .md-items-center {
        align-items: center;
    }

    .md-justify-center {
        justify-content: center;
    }

    .md-space-y-4 > * + * {
        margin-top: 1rem;
    }

    .md-mt-4 {
        margin-top: 1rem;
    }

    .md-mb-4 {
        margin-bottom: 1rem;
    }
}
