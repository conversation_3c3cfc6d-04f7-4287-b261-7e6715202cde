import type { UseFetchOptions } from '#app'

export interface RefreshTokenResponse {
    accessToken: string
    refreshToken: string
}

export interface PaginationMeta {
    current_page: number;
    per_page: number;
    total_count: number;
    total_pages: number;
}

export interface PaginatedApiResponse<T> {
    data: T[];
    pagination: PaginationMeta;
}

export interface ApiInstance {
    get: <T>(url: string, options?: UseFetchOptions<T>) => Promise<T>
    post: <T>(url: string, body: any, options?: UseFetchOptions<T>) => Promise<T>
    put: <T>(url: string, body: any, options?: UseFetchOptions<T>) => Promise<T>
    patch: <T>(url: string, body: any, options?: UseFetchOptions<T>) => Promise<T>
    delete: <T>(url: string, options?: UseFetchOptions<T>) => Promise<T>
    getTokens: () => {
        accessToken: string | null
        refreshToken: string | null
    }
    saveTokens: (accessToken: string, refreshToken: string) => void
    clearTokens: () => void
    refreshTokens: () => Promise<string>
}
