export interface ApiResponse<T> {
    data: T;
    status: number;
    statusText: string;
}

export interface ApiOptions {
    headers?: Record<string, string>;
    params?: Record<string, any>;
}

export interface Metadata {
    drugs: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any, options?: ApiOptions) => Promise<ApiResponse<any>>;
        update: (id: number, data: any, options?: ApiOptions) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    };
    organisms: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    };
    test_statuses: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    };
    departments: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    };
    specimen_types: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    };
    measure_types: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    };
    lab_test_sites: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    },
    equipments: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    },
    products: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    },
    test_panels: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    },
    users: {
        getAll: (params?: Record<string, any>) => Promise<ApiResponse<any>>;
        getById: (id: number) => Promise<ApiResponse<any>>;
        create: (data: any) => Promise<ApiResponse<any>>;
        update: (id: number, data: any) => Promise<ApiResponse<any>>;
        delete: (id: number) => Promise<ApiResponse<any>>;
    }
}