export type ValidationErrorType =
    | 'required'
    | 'email'
    | 'password'
    | 'minLength'
    | 'maxLength'
    | 'pattern'
    | 'match'
    | 'array'
    | 'custom';

export interface ValidationErrorMessages {
    required?: string;
    email?: string;
    password?: string;
    minLength?: string;
    maxLength?: string;
    pattern?: string;
    match?: string;
    custom?: string;
    array?: string;
}

export interface FieldError {
    type: ValidationErrorType;
    message: string;
    params?: Record<string, any>;
}

export interface FormErrors {
    [fieldName: string]: FieldError | undefined;
}

export interface ValidationRules {
    required?: boolean;
    email?: boolean;
    password?: {
        minLength?: number;
        requireUppercase?: boolean;
        requireLowercase?: boolean;
        requireNumbers?: boolean;
        requireSpecialChars?: boolean;
    };
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    match?: string;
    array?: boolean;
    custom?: (value: any, formValues: Record<string, any>) => boolean | string;
}

export interface FieldConfig {
    name: string;
    label?: string;
    rules?: ValidationRules;
    errorMessages?: ValidationErrorMessages;
}

export interface FormConfig {
    fields: FieldConfig[];
    defaultErrorMessages?: ValidationErrorMessages;
}

export interface ValidationResult {
    valid: boolean;
    errors: FormErrors;
}

type ValidatorFn = (
    values: Record<string, any>,
    config: FormConfig
) => ValidationResult;

export interface FormContext {
    formValues: Record<string, any>;
    formErrors: FormErrors;
    isValid: Ref<boolean>;
    validateField: (name: string) => void;
    validateAll: () => { valid: boolean; errors: FormErrors };
    resetForm: () => void;
}