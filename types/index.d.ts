export interface MeasureRange {
    id?: number;
    age_min: number;
    age_max: number;
    range_lower: number;
    range_upper: number;
    sex: string;
    value: string;
    interpretation: string;
}

export interface MeasureAutocomplete {
    id?: number;
    value: string;
    interpretation: string;
}

export interface Measure {
    id?: number;
    name: string;
    short_name: string;
    unit: string;
    measure_type_id: number;
    description: string;
    loinc_code: string;
    moh_code: string;
    nlims_code: string;
    preferred_name: string;
    scientific_name: string;
    measure_ranges_attributes: (MeasureRange | MeasureAutocomplete)[];
}

export interface TestType {
    id: number;
    test_category_id: number;
    name: string;
    short_name: string;
    targetTAT: string;
    description: string;
    prevalence_threshold: number | null;
    created_at: string;
    updated_at: string;
    moh_code: string | null;
    nlims_code: string | null;
    loinc_code: string | null;
    preferred_name: string | null;
    scientific_name: string | null;
    can_be_done_on_sex: string;
    assay_format: string | null;
    hr_cadre_required: string | null;
    iblis_mapping_name: string | null;
}

export interface TestCatalog {
    test_type: TestType;
    specimen_types: number[];
    measures: Measure[];
    organisms: number[];
    lab_test_sites?: number[];
    equipment: number[];
}

export interface LabTestSite {
    id: number;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
}

export interface Specimen {
    id: number,
    name: string,
    description: string,
    created_at: string,
    updated_at: string,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    preferred_name: string | null,
    scientific_name: string | null,
    iblis_mapping_name: string | null
}

export interface Department {
    id: number,
    name: string,
    description: string,
    created_at: string,
    updated_at: string,
    short_name: string | null,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    preferred_name: string | null,
    scientific_name: string | null
}

export interface TestStatus {
    id: number,
    name: string,
    test_phase_id: number,
    created_at: string,
    updated_at: string,
    short_name: string | null,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    preferred_name: string | null,
    scientific_name: string | null,
    description: string | null
}

export interface Drug {
    id: number,
    name: string,
    short_name: string;
    description: string,
    created_at: string,
    updated_at: string,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    preferred_name: string | null,
    scientific_name: string | null
}

export interface Equipment {
    id: number,
    name: string,
    description: string,
    created_at: string,
    updated_at: string,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    products?: Product[]
}

export interface Product {
    id: number,
    name: string,
    description: string,
    created_at: string,
    updated_at: string,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
}

export interface Organism {
    id: number,
    name: string,
    short_name: string;
    description: string,
    created_at: string,
    updated_at: string,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    preferred_name: string | null,
    scientific_name: string | null,
    drugs?: number[]
}

export interface Parameter {
    name: string;
    type: string;
    values: string | string[] | Range[];
}

export interface Range {
    min?: string;
    max?: string;
}

export interface Structure {
    type: "options" | "range" | "ranges" | "string" | undefined;
    parameters?: Parameter[];
}

export interface MeasureType {
    id: number,
    name: string,
    description: string,
    structure?: Structure,
    created_at: string,
    updated_at: string,
    moh_code: string | null,
    nlims_code: string | null,
    loinc_code: string | null,
    preferred_name: string | null,
    scientific_name: string | null
}

export interface ReleaseNote {
    id: number;
    title: string;
    description: string;
    changeType: string;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by: string;
}

export interface TestPanel {
    id: number;
    name: string;
    description: string;
    short_name: string;
    moh_code: string | null;
    nlims_code: string | null;
    loinc_code: string | null;
    preferred_name: string | null;
    scientific_name: string | null;
    created_at: string;
    updated_at: string;
    test_types: number[];
}

export interface Version {
    id: number;
    version: string;
    catalog: {
        drugs: Drug[];
        organisms: Organism[];
        test_types: TestType[];
        test_panels: TestPanel[];
        specimen_types: Specimen[];
    }
    version_details: { releaseNotes: ReleaseNote[] };
    created_at: string;
}


export interface ViewTestType {
    id: number;
    name: string;
    short_name: string;
    targetTAT: string;
    description: string;
    prevalence_threshold: number | null;
    created_at: string;
    updated_at: string;
    moh_code: string;
    nlims_code: string;
    loinc_code: string;
    preferred_name: string;
    scientific_name: string;
    can_be_done_on_sex: string;
    assay_format: string;
    hr_cadre_required: string;
    iblis_mapping_name: string;
    test_category: Department;
    specimen_types: Specimen[];
    measures: Measure[];
    organisms: Organism[];
    lab_test_sites: LabTestSite[];
    equipment: Equipment[];
}

export interface User {
    id: number;
    username: string;
    app_name: string;
    partner: string;
    location: string;
    created_at: string;
    updated_at: string;
}