import type { FormConfig, ValidationResult } from "./input";

interface Option {
    id: string | number;
    label: string;
    [key: string]: any;
}

interface Step {
    id: number;
    title: string;
    filled?: boolean;
    valid?: boolean;
    component?: any;
    props?: object;
    validationSchema: FormConfig;
    validate?: () => ValidationResult;
    values?: Record<string, any>;
    initialData?: object;
    emittedData?: Object;
}

interface Credentials {
    username: string;
    password: string;
}

export type {
    Option,
    Step,
    Credentials
}