import { defineNuxtPlugin, type UseFetchOptions } from '#app'
import { defu } from 'defu'
import { FetchError } from 'ofetch'
import { useAuthStore } from '@/store/auth'

interface ApiOptions {
    baseURL: string
    tokenStorage?: 'localStorage' | 'cookie'
    accessTokenKey?: string
    refreshTokenKey?: string
    refreshTokenEndpoint?: string
}

interface ProgressEvent {
    loaded: number
    total?: number
    progress?: number
}

interface ApiRequestOptions<T> extends UseFetchOptions<T> {
    onUploadProgress?: (progressEvent: ProgressEvent) => void
    onDownloadProgress?: (progressEvent: ProgressEvent) => void
}

export default defineNuxtPlugin(() => {
    const config = useRuntimeConfig()
    const router = useRouter()

    const defaultOptions: ApiOptions = {
        baseURL: `${config.public.apiBaseUrl}/api/v1/`,
        tokenStorage: 'localStorage',
        accessTokenKey: 'accessToken',
        refreshTokenKey: 'refreshToken',
        refreshTokenEndpoint: 'refresh_token/'
    }

    const options = defu(config.public.api || {}, defaultOptions)

    const getTokens = (): { accessToken: string | null, refreshToken: string | null } => {
        if (options.tokenStorage === 'localStorage') {
            if (import.meta.server) return { accessToken: null, refreshToken: null }

            return {
                accessToken: localStorage.getItem(options.accessTokenKey!),
                refreshToken: localStorage.getItem(options.refreshTokenKey!)
            }
        } else {
            const cookies = useCookie(options.accessTokenKey!)
            const refreshCookies = useCookie(options.refreshTokenKey!)

            return {
                accessToken: cookies.value || null,
                refreshToken: refreshCookies.value || null
            }
        }
    }

    const saveTokens = (accessToken: string, refreshToken: string): void => {
        if (options.tokenStorage === 'localStorage') {
            if (import.meta.server) return

            localStorage.setItem(options.accessTokenKey!, accessToken)
            localStorage.setItem(options.refreshTokenKey!, refreshToken)
        } else {
            const cookies = useCookie(options.accessTokenKey!)
            const refreshCookies = useCookie(options.refreshTokenKey!)

            cookies.value = accessToken
            refreshCookies.value = refreshToken
        }

        const authStore = useAuthStore()
        authStore.setToken(accessToken)
    }

    const clearTokens = (): void => {
        if (options.tokenStorage === 'localStorage') {
            if (import.meta.server) return

            localStorage.removeItem(options.accessTokenKey!)
            localStorage.removeItem(options.refreshTokenKey!)
        } else {
            const cookies = useCookie(options.accessTokenKey!)
            const refreshCookies = useCookie(options.refreshTokenKey!)
            cookies.value = null
            refreshCookies.value = null
        }

        const authStore = useAuthStore()
        authStore.clearToken();
    }

    let isRefreshing = false
    let failedQueue: Array<{
        resolve: (token: string) => void;
        reject: (error: Error) => void;
    }> = []

    const processQueue = (error: Error | null, token: string | null = null) => {
        failedQueue.forEach(promise => {
            if (error) {
                promise.reject(error)
            } else {
                promise.resolve(token!)
            }
        })

        failedQueue = []
    }

    const refreshTokens = async (): Promise<string> => {
        if (isRefreshing) {
            return new Promise((resolve, reject) => {
                failedQueue.push({ resolve, reject })
            })
        }

        isRefreshing = true

        const authStore = useAuthStore()

        if (!authStore.user?.app_uuid) {
            isRefreshing = false
            clearTokens()
            throw new Error('No refresh token available')
        }

        try {
            const response: any = await api.post(options.refreshTokenEndpoint!, { app_uuid: authStore.user?.app_uuid })
            authStore.setToken(response.data.token)
            authStore.user = response.user
            return response.data.token
        } catch (error) {
            isRefreshing = false
            clearTokens()
            processQueue(new Error('Failed to refresh token'))
            throw error
        }
    }

    const createProgressTracker = (
        onProgress?: (progress: number, status: string) => void
    ) => {
        return {
            onUploadProgress: (progressEvent: ProgressEvent) => {
                if (onProgress && progressEvent.total) {
                    const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                    const status = progress < 100 ? `Uploading... ${progress}%` : 'Processing...';
                    onProgress(progress, status);
                }
            },
            onDownloadProgress: (progressEvent: ProgressEvent) => {
                if (onProgress && progressEvent.total) {
                    const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
                    const status = `Downloading... ${progress}%`;
                    onProgress(progress, status);
                }
            }
        };
    };

    const apiFetch = async<T>(
        url: string,
        fetchOptions: ApiRequestOptions<T> = {}
    ) => {
        const authStore = useAuthStore();
        const { accessToken } = getTokens();

        const token = authStore.token || accessToken;

        const options: any = defu(fetchOptions, {
            baseURL: defaultOptions.baseURL,
            headers: token ? {
                token: `${token}`
            } : { 'Content-Type': 'application/json' },
            onUploadProgress: fetchOptions.onUploadProgress,
            onDownloadProgress: fetchOptions.onDownloadProgress,
        })

        try {
            return await $fetch<T>(url, options)
        } catch (error) {
            if (error instanceof FetchError) {
                if (error.response?.status === 401) {
                    if (authStore.user?.app_uuid) {
                        try {
                            const newToken = await refreshTokens();
                            options.headers.token = `${newToken}`
                            return await $fetch<T>(url, options)
                        } catch (refreshError) {
                            clearTokens()
                            if (import.meta.client) {
                                router.push('/')
                            }
                            throw refreshError
                        }
                    } else {
                        clearTokens()
                        if (import.meta.client) {
                            router.push('/')
                        }
                    }
                }

                if (error.response?.status === 422) {
                    const validationErrors = error.response?._data?.errors || error.response?._data?.message;
                    const validationError = new Error('Validation error');
                    // @ts-ignore
                    validationError.validation = validationErrors;
                    // @ts-ignore
                    validationError.status = 422;
                }
            }

            throw error
        }
    }

    const api = {
        get: <T>(url: string, options: ApiRequestOptions<T> = {}) =>
            apiFetch<T>(url, { ...options, method: 'GET' }),

        post: <T>(url: string, body: any, options: ApiRequestOptions<T> = {}) =>
            apiFetch<T>(url, { ...options, method: 'POST', body }),

        put: <T>(url: string, body: any, options: ApiRequestOptions<T> = {}) =>
            apiFetch<T>(url, { ...options, method: 'PUT', body }),

        patch: <T>(url: string, body: any, options: ApiRequestOptions<T> = {}) =>
            apiFetch<T>(url, { ...options, method: 'PATCH', body }),

        delete: <T>(url: string, options: ApiRequestOptions<T> = {}) =>
            apiFetch<T>(url, { ...options, method: 'DELETE' }),

        // Helper methods for file uploads with progress
        postWithProgress: <T>(
            url: string,
            body: any,
            onProgress?: (progress: number, status: string) => void
        ) => {
            const progressConfig = createProgressTracker(onProgress);
            return apiFetch<T>(url, {
                method: 'POST',
                body,
                ...progressConfig
            });
        },

        putWithProgress: <T>(
            url: string,
            body: any,
            onProgress?: (progress: number, status: string) => void
        ) => {
            const progressConfig = createProgressTracker(onProgress);
            return apiFetch<T>(url, {
                method: 'PUT',
                body,
                ...progressConfig
            });
        },

        getTokens,
        saveTokens,
        clearTokens,
        refreshTokens,
        createProgressTracker
    }

    return {
        provide: {
            api
        }
    }
})
