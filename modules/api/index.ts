import { defineNuxtModule, addPlugin, createResolver } from '@nuxt/kit'
import { defu } from 'defu'

export interface ModuleOptions {
  baseURL?: string
  tokenStorage?: 'localStorage' | 'cookie'
  accessTokenKey?: string
  refreshTokenKey?: string
  refreshTokenEndpoint?: string
}

export default defineNuxtModule<ModuleOptions>({
  meta: {
    name: 'nuxt-api-auth',
    configKey: 'apiAuth',
    compatibility: {
      nuxt: '^3.0.0'
    }
  },
  defaults: {
    baseURL: process.env.API_BASE_URL,
    tokenStorage: 'localStorage',
    accessTokenKey: 'accessToken',
    refreshTokenKey: 'refreshToken',
    refreshTokenEndpoint: 'refresh_token/'
  },
  setup(options, nuxt) {
    const resolver = createResolver(import.meta.url)
    
    nuxt.options.runtimeConfig.public.api = defu(
      nuxt.options.runtimeConfig.public.api || {},
      options
    )
    
    addPlugin(resolver.resolve('./runtime/plugin'))
  }
});