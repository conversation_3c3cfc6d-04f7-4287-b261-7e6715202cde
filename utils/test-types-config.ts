import type { FormConfig } from "@/types/input";

export const formConfig: FormConfig = {
    fields: [
        {
            name: 'test_type_name',
            label: 'Test type name',
            rules: { required: true }
        },
        {
            name: 'preferred_name',
            label: 'Preferred name',
            rules: { required: true }
        },
        {
            name: 'scientific_name',
            label: 'Scientific name',
            rules: { required: true }
        },
        {
            name: 'short_name',
            label: 'Short name',
            rules: { required: true }
        },
        {
            name: 'measure_lonic_code',
            label: 'Lonic code',
            rules: { required: true }
        },
        {
            name: 'moh_code',
            label: 'MOH code',
            rules: { required: true }
        },
        {
            name: 'target_tat',
            label: 'Target TAT',
            rules: { required: true }
        },
        {
            name: 'measures',
            label: 'Measures',
            rules: { 
                required: true, 
            }
        },
        {
            name: 'measure_name',
            label: 'Measure Name',
            rules: { required: true }
        },
        {
            name: 'measure_short_name',
            label: 'Measure Short Name',
            rules: { required: true }
        },
        {
            name: 'measure_preferred_name',
            label: 'Preferred Name',
            rules: { required: true }
        },
        {
            name: 'measure_scientific_name',
            label: 'Scientific Name',
            rules: { required: true }
        },
        {
            name: 'measure_unit',
            label: 'Measure Unit',
            rules: { required: true }
        },
        {
            name: 'measure_loinc_code',
            label: 'LONIC Code',
            rules: { required: true }
        },
        {
            name: 'measure_moh_code',
            label: 'MOH Code',
            rules: { required: true }
        },
        {
            name: 'measure_measure_ranges_attributes',
            label: 'Reference Ranges',
            rules: { 
                required: true, 
            }
        },
        {
            name: 'measure-age-range-min',
            label: 'Age Range Min',
            rules: { required: true }
        },
        {
            name: 'measure-age-range-max',
            label: 'Age Range Max',
            rules: { required: true }
        },
        {
            name: 'measure-value',
            label: 'Value',
            rules: { required: true }
        },
        {
            name: 'range-lower',
            label: 'Range Lower',
            rules: { required: true }
        },
        {
            name: 'range-upper',
            label: 'Range Upper',
            rules: { required: true }
        },
        {
            name: 'interpretation',
            label: 'Interpretation',
            rules: { required: true }
        }
    ]
};